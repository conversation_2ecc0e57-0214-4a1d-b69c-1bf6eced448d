'use client';

import { useRouter } from 'next/navigation';
import { Button } from '../ui/button';
import { createSupabaseBrowserClient } from '../../lib/supabase/client';

export function LogoutButton() {
  const router = useRouter();

  async function handleLogout() {
    const supabase = createSupabaseBrowserClient();
    const { error } = await supabase.auth.signOut();
    if (!error) {
      router.push('/login');
    } else {
      console.error('Error signing out:', error.message);
    }
  }

  return <Button onClick={handleLogout}>Logout</Button>;
}
