import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { useRouter } from 'next/navigation';
import UserProfile from '../UserProfile';

// Mock next/navigation
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
}));

// Mock useAuth from AuthProvider
jest.mock('../AuthProvider', () => ({
  useAuth: jest.fn(),
}));

import { useAuth } from '../AuthProvider';

// Mock the Supabase client module
jest.mock('@/lib/supabase/client', () => {
  const mockFrom = {
    select: jest.fn(),
    update: jest.fn(),
  };

  const mockAuth = {
    signOut: jest.fn(),
  };

  return {
    createSupabaseBrowserClient: jest.fn(() => ({
      from: jest.fn(() => mockFrom),
      auth: mockAuth,
    })),
    // Export the mocks for test access
    __mockFrom: mockFrom,
    __mockAuth: mockAuth,
  };
});

// Get access to the mock objects
const { __mockFrom: mockFrom } = jest.requireMock('@/lib/supabase/client');

const mockPush = jest.fn();
const mockedUseAuth = useAuth as jest.Mock;
const mockedUseRouter = useRouter as jest.Mock;

type ProfileData = {
  id: string;
  full_name: string | null;
};

type SupabaseError = {
  message: string;
  code?: string;
};

function setupSupabaseMocks({
  fetchProfileResult,
  fetchProfileError,
  updateProfileResult,
  updateProfileError,
}: {
  fetchProfileResult?: ProfileData | null;
  fetchProfileError?: SupabaseError | null;
  updateProfileResult?: ProfileData | null;
  updateProfileError?: SupabaseError | null;
}) {
  // Reset mocks
  mockFrom.select.mockClear();
  mockFrom.update.mockClear();

  // Setup chainable methods for profile fetch: .select().eq().single()
  const fetchSingle = jest.fn();
  const fetchEq = jest.fn(() => ({ single: fetchSingle }));
  mockFrom.select.mockReturnValue({ eq: fetchEq });

  // Setup chainable methods for profile update: .update().eq().select().single()
  const updateSingle = jest.fn();
  const updateSelect = jest.fn(() => ({ single: updateSingle }));
  const updateEq = jest.fn(() => ({ select: updateSelect }));
  mockFrom.update.mockReturnValue({ eq: updateEq });

  // Configure fetch response
  fetchSingle.mockResolvedValue({
    data: fetchProfileError ? null : fetchProfileResult,
    error: fetchProfileError || null,
  });

  // Configure update response
  updateSingle.mockResolvedValue({
    data: updateProfileError ? null : updateProfileResult,
    error: updateProfileError || null,
  });
}

describe('UserProfile', () => {
  const user = { email: '<EMAIL>', id: '123' };

  beforeEach(() => {
    // Reset all mocks before each test
    jest.clearAllMocks();

    // Setup router mock
    mockedUseRouter.mockReturnValue({
      push: mockPush,
    });
  });

  it('shows loading state while profile is loading', async () => {
    mockedUseAuth.mockReturnValue({ user });
    // Setup mocks for a delayed response to show loading state
    setupSupabaseMocks({ fetchProfileResult: { id: user.id, full_name: 'Test User' } });

    render(<UserProfile />);
    // Should show loading immediately
    expect(screen.getByText('Loading profile...')).toBeInTheDocument();
  });

  it('displays user email and full name after successful fetch', async () => {
    mockedUseAuth.mockReturnValue({ user });
    setupSupabaseMocks({
      fetchProfileResult: { id: user.id, full_name: 'Test User' },
    });

    render(<UserProfile />);
    // Wait for full name to appear
    expect(await screen.findByDisplayValue('Test User')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    expect(screen.getByLabelText('Full Name')).toBeInTheDocument();
  });

  it('shows empty input if user has no full_name', async () => {
    mockedUseAuth.mockReturnValue({ user });
    setupSupabaseMocks({
      fetchProfileResult: { id: user.id, full_name: null },
    });

    render(<UserProfile />);
    const input = await screen.findByLabelText('Full Name');
    expect(input).toHaveValue('');
  });

  it('shows error message if fetching profile fails', async () => {
    mockedUseAuth.mockReturnValue({ user });
    setupSupabaseMocks({
      fetchProfileError: { message: 'Network error', code: '500' },
    });

    render(<UserProfile />);
    expect(await screen.findByRole('alert')).toHaveTextContent(
      'Failed to load profile: Network error'
    );
  });

  it('allows user to update full name via form', async () => {
    mockedUseAuth.mockReturnValue({ user });
    // Initial fetch returns no name
    setupSupabaseMocks({
      fetchProfileResult: { id: user.id, full_name: '' },
      updateProfileResult: { id: user.id, full_name: 'New Name' },
    });

    render(<UserProfile />);
    const input = await screen.findByLabelText('Full Name');
    expect(input).toHaveValue('');

    // Type new name
    await userEvent.type(input, 'New Name');
    // Submit form
    const button = screen.getByRole('button', { name: /update full name/i });
    expect(button).not.toBeDisabled();
    await userEvent.click(button);

    // Wait for success message
    expect(await screen.findByText('Full name updated successfully.')).toBeInTheDocument();
    // Input should be updated and not dirty
    expect(input).toHaveValue('New Name');
  });

  it('shows error if updating full name fails', async () => {
    mockedUseAuth.mockReturnValue({ user });
    setupSupabaseMocks({
      fetchProfileResult: { id: user.id, full_name: '' },
      updateProfileError: { message: 'Update failed', code: '500' },
    });

    render(<UserProfile />);
    const input = await screen.findByLabelText('Full Name');
    await userEvent.type(input, 'Fail Name');
    const button = screen.getByRole('button', { name: /update full name/i });
    await userEvent.click(button);

    expect(await screen.findByRole('alert')).toHaveTextContent(
      'Failed to update full name: Update failed'
    );
  });

  it('shows "Loading user..." if user is null', () => {
    mockedUseAuth.mockReturnValue({ user: null });
    render(<UserProfile />);
    expect(screen.getByText('Loading user...')).toBeInTheDocument();
  });
});
