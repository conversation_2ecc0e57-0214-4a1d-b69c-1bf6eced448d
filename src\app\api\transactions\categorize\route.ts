import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClient } from '@/lib/supabase/server';

export async function POST(request: NextRequest) {
  const supabase = await createSupabaseServerClient();

  // Check authentication
  const {
    data: { user },
    error,
  } = await supabase.auth.getUser();

  if (error || !user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  const userId = user.id;

  // Parse request body
  let body;
  try {
    body = await request.json();
  } catch {
    return NextResponse.json({ error: 'Invalid JSON body' }, { status: 400 });
  }

  const { transaction_id, user_category_id } = body;

  if (!transaction_id || !user_category_id) {
    return NextResponse.json(
      { error: 'transaction_id and user_category_id are required' },
      { status: 400 }
    );
  }

  try {
    // Verify transaction ownership
    const { data: transaction, error: transactionError } = await supabase
      .from('transactions')
      .select('id, user_id, user_category_id')
      .eq('id', transaction_id)
      .single();

    if (transactionError || !transaction) {
      return NextResponse.json(
        { error: 'Transaction not found' },
        { status: 404 }
      );
    }

    if (transaction.user_id !== userId) {
      return NextResponse.json(
        { error: 'Unauthorized to update this transaction' },
        { status: 403 }
      );
    }

    // Verify category ownership
    const { data: category, error: categoryError } = await supabase
      .from('user_categories')
      .select('id, user_id')
      .eq('id', user_category_id)
      .single();

    if (categoryError || !category) {
      return NextResponse.json(
        { error: 'Category not found' },
        { status: 404 }
      );
    }

    if (category.user_id !== userId) {
      return NextResponse.json(
        { error: 'Unauthorized to use this category' },
        { status: 403 }
      );
    }

    // Update transaction with new user_category_id
    const { data: updatedTransaction, error: updateError } = await supabase
      .from('transactions')
      .update({ user_category_id })
      .eq('id', transaction_id)
      .single();

    if (updateError) {
      return NextResponse.json(
        { error: 'Failed to update transaction category' },
        { status: 500 }
      );
    }

    return NextResponse.json(updatedTransaction, { status: 200 });
  } catch (error) {
    console.error('[TRANSACTIONS_CATEGORIZE_ERROR]', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}