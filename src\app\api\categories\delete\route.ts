import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClient } from '@/lib/supabase/server';

export async function DELETE(request: NextRequest) {
  const supabase = await createSupabaseServerClient();

  // Check authentication
  const {
    data: { user },
    error,
  } = await supabase.auth.getUser();

  if (error || !user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  const userId = user.id;

  try {
    // Parse request body
    const body = await request.json();
    const { category_id } = body;

    // Validate required fields
    if (!category_id || typeof category_id !== 'string') {
      return NextResponse.json(
        { error: 'Category ID is required and must be a string' },
        { status: 400 }
      );
    }

    // First, verify the category exists and belongs to the user
    const { data: existingCategory, error: fetchError } = await supabase
      .from('user_categories')
      .select('id, name')
      .eq('id', category_id)
      .eq('user_id', userId)
      .single();

    if (fetchError || !existingCategory) {
      return NextResponse.json(
        { error: 'Category not found or access denied' },
        { status: 404 }
      );
    }

    // Check if the category is being used by any transactions
    const { count: transactionCount, error: countError } = await supabase
      .from('transactions')
      .select('*', { count: 'exact', head: true })
      .eq('user_category_id', category_id)
      .eq('user_id', userId);

    if (countError) {
      console.error('Error checking transaction usage:', countError);
      return NextResponse.json(
        { error: 'Failed to verify category usage' },
        { status: 500 }
      );
    }

    // If category is in use, prevent deletion
    if (transactionCount && transactionCount > 0) {
      return NextResponse.json(
        { 
          error: 'Cannot delete category that is assigned to transactions',
          details: `This category is currently assigned to ${transactionCount} transaction(s)`
        },
        { status: 409 }
      );
    }

    // Delete the category
    const { error: deleteError } = await supabase
      .from('user_categories')
      .delete()
      .eq('id', category_id)
      .eq('user_id', userId);

    if (deleteError) {
      console.error('Error deleting category:', deleteError);
      return NextResponse.json(
        { error: 'Failed to delete category' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      message: 'Category deleted successfully',
      deleted_category: {
        id: category_id,
        name: existingCategory.name
      }
    });
  } catch (error) {
    console.error('[CATEGORIES_DELETE_ERROR]', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}