-- System and user-defined transaction categories
CREATE TABLE transaction_categories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES profiles(id) ON DELETE CASCADE, -- NULL for system categories
    name TEXT NOT NULL,
    description TEXT,
    category_type TEXT NOT NULL CHECK (category_type IN ('system', 'user')),
    parent_category_id UUID REFERENCES transaction_categories(id) ON DELETE SET NULL,
    icon TEXT,
    color TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(user_id, name) -- Allow same name for different users, but unique per user
);

-- Indexes
CREATE INDEX idx_transaction_categories_user_id ON transaction_categories(user_id);
CREATE INDEX idx_transaction_categories_type ON transaction_categories(category_type);
CREATE INDEX idx_transaction_categories_parent ON transaction_categories(parent_category_id);

-- Enable RLS
ALTER TABLE transaction_categories ENABLE ROW LEVEL SECURITY;

-- RLS Policies (system categories visible to all, user categories only to owner)
CREATE POLICY "Users can view system and own categories" ON transaction_categories
    FOR SELECT
    TO authenticated
    USING (category_type = 'system' OR (SELECT auth.uid()) = user_id);

CREATE POLICY "Users can insert own categories" ON transaction_categories
    FOR INSERT
    TO authenticated
    WITH CHECK ((SELECT auth.uid()) = user_id AND category_type = 'user');

CREATE POLICY "Users can update own categories" ON transaction_categories
    FOR UPDATE
    TO authenticated
    USING ((SELECT auth.uid()) = user_id AND category_type = 'user')
    WITH CHECK ((SELECT auth.uid()) = user_id AND category_type = 'user');

CREATE POLICY "Users can delete own categories" ON transaction_categories
    FOR DELETE
    TO authenticated
    USING ((SELECT auth.uid()) = user_id AND category_type = 'user');

-- Trigger for updated_at
CREATE TRIGGER update_transaction_categories_updated_at BEFORE UPDATE ON transaction_categories
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Comments
COMMENT ON TABLE transaction_categories IS 'System and user-defined transaction categories';
 