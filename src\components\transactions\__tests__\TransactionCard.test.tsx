import React from 'react';
import { render, screen } from '@testing-library/react';
import TransactionCard from '../TransactionCard';

// Mock the CategorySelector component
jest.mock('@/components/categories/CategorySelector', () => {
  return function MockCategorySelector(props: {
    transaction_id: string;
    user_category_id: string | null;
  }) {
    return (
      <div
        data-testid='mock-category-selector'
        data-transaction-id={props.transaction_id}
        data-user-category-id={props.user_category_id || ''}
      >
        Mock CategorySelector
      </div>
    );
  };
});

describe('TransactionCard', () => {
  const mockTransaction = {
    id: 'txn_123',
    amount: -123.45,
    currency_code: 'USD',
    transaction_date: '2025-06-08T12:00:00.000Z',
    authorized_date: null,
    posted_date: null,
    merchant_name: 'Test Merchant',
    description: 'Test Description',
    category_id: 'cat_1',
    user_category_id: 'user_cat_1',
    plaid_category: ['Shopping'],
    plaid_category_detailed: ['Shopping', 'Retail'],
    transaction_type: 'debit',
    location: null,
    is_pending: false,
    is_recurring: false,
    status: 'posted',
    tags: null,
    notes: 'Test note for transaction',
    created_at: '2025-06-08T12:00:00.000Z',
    updated_at: '2025-06-08T12:00:00.000Z',
    financial_accounts: {
      id: 'acc_1',
      account_name: 'Checking Account',
      institution_name: 'Test Bank',
      account_type: 'depository',
      account_subtype: 'checking',
      mask: '1234',
    },
  };

  test('renders correct transaction data', () => {
    render(<TransactionCard transaction={mockTransaction} />);

    // Merchant or description
    expect(screen.getByText('Test Merchant')).toBeInTheDocument();

    // Account info
    expect(screen.getByText('Checking Account')).toBeInTheDocument();
    expect(screen.getByText('Test Bank')).toBeInTheDocument();
    expect(screen.getByText('****1234')).toBeInTheDocument();

    // Transaction date formatted
    expect(screen.getByText('Jun 8, 2025')).toBeInTheDocument();

    // Amount formatted with minus sign and currency
    expect(screen.getByText('-$123.45')).toBeInTheDocument();

    // Transaction type
    expect(screen.getByText('debit')).toBeInTheDocument();

    // Notes
    expect(screen.getByText('Test note for transaction')).toBeInTheDocument();

    // Plaid category badge
    expect(screen.getByText('Shopping')).toBeInTheDocument();
  });

  test('renders mocked CategorySelector with correct props', () => {
    render(<TransactionCard transaction={mockTransaction} />);

    const categorySelector = screen.getByTestId('mock-category-selector');
    expect(categorySelector).toBeInTheDocument();

    // Check props passed to mocked CategorySelector
    expect(categorySelector.getAttribute('data-transaction-id')).toBe(mockTransaction.id);
    expect(categorySelector.getAttribute('data-user-category-id')).toBe(
      mockTransaction.user_category_id
    );
  });
});
