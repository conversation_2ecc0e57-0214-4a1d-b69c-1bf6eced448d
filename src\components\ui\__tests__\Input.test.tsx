import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { Input } from '../input';

describe('Input component', () => {
  test('renders correctly', () => {
    render(<Input />);
    const inputElement = screen.getByRole('textbox');
    expect(inputElement).toBeInTheDocument();
  });

  test('updates value when user types', async () => {
    render(<Input />);
    const inputElement = screen.getByRole('textbox');
    const user = userEvent.setup();
    const text = 'Hello, world!';
    await user.type(inputElement, text);
    expect(inputElement).toHaveValue(text);
  });
});
