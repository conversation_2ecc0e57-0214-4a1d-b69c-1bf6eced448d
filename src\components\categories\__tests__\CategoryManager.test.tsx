import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import CategoryManager from '../CategoryManager';

// Mock global fetch
global.fetch = jest.fn();
global.confirm = jest.fn();

// Mock react-hook-form
const mockReset = jest.fn();
const mockHandleSubmit = jest.fn((fn) => (e) => {
  e.preventDefault();
  const formData = {
    name: 'Test Category',
    description: 'Test Description',
    color: '#ef4444',
    icon: '🍔',
  };
  fn(formData);
});

jest.mock('react-hook-form', () => ({
  useForm: () => ({
    control: {},
    handleSubmit: mockHandleSubmit,
    reset: mockReset,
  }),
}));

// Mock UI components with simple implementations
jest.mock('@/components/ui/LoadingSpinner', () => ({
  __esModule: true,
  LoadingSpinner: () => <div data-testid="loading-spinner">Loading...</div>,
}));

jest.mock('@/components/ui/button', () => ({
  __esModule: true,
  Button: ({ children, onClick, disabled, type, variant, size, ...props }: any) => (
    <button
      onClick={onClick}
      disabled={disabled}
      type={type}
      data-variant={variant}
      data-size={size}
      {...props}
    >
      {children}
    </button>
  ),
}));

jest.mock('@/components/ui/input', () => ({
  __esModule: true,
  Input: React.forwardRef<HTMLInputElement, any>((props, ref) => <input ref={ref} {...props} />),
}));

jest.mock('@/components/ui/card', () => ({
  __esModule: true,
  Card: ({ children, ...props }: any) => <div data-testid="card" {...props}>{children}</div>,
  CardContent: ({ children, ...props }: any) => <div data-testid="card-content" {...props}>{children}</div>,
  CardDescription: ({ children, ...props }: any) => <div data-testid="card-description" {...props}>{children}</div>,
  CardHeader: ({ children, ...props }: any) => <div data-testid="card-header" {...props}>{children}</div>,
  CardTitle: ({ children, ...props }: any) => <div data-testid="card-title" {...props}>{children}</div>,
}));

jest.mock('@/components/ui/form', () => ({
  __esModule: true,
  Form: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  FormControl: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  FormField: ({ render, name }: any) => {
    const field = {
      value: '',
      onChange: jest.fn(),
      onBlur: jest.fn(),
      name,
    };
    return render({ field });
  },
  FormItem: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  FormLabel: ({ children, ...props }: any) => <label {...props}>{children}</label>,
  FormMessage: ({ children, ...props }: any) => <div {...props}>{children}</div>,
}));

describe('CategoryManager Component', () => {
  const mockCategories = [
    {
      id: 'cat-1',
      name: 'Food & Dining',
      description: 'Restaurant and food purchases',
      color: '#ef4444',
      icon: '🍔',
      is_active: true,
      created_at: '2025-01-01T00:00:00Z',
      updated_at: '2025-01-02T00:00:00Z',
    },
    {
      id: 'cat-2',
      name: 'Transportation',
      description: 'Gas and car expenses',
      color: '#3b82f6',
      icon: '🚗',
      is_active: true,
      created_at: '2025-01-03T00:00:00Z',
      updated_at: '2025-01-04T00:00:00Z',
    },
  ];

  beforeEach(() => {
    jest.clearAllMocks();
    (global.confirm as jest.Mock).mockReturnValue(true);
  });

  // Test Case 1: Initial Load and Display
  describe('Initial Load and Display', () => {
    test('shows loading spinner initially and then displays categories', async () => {
      // Mock successful fetch response
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ categories: mockCategories }),
      });

      render(<CategoryManager />);

      // Should show loading spinner initially
      expect(screen.getByTestId('loading-spinner')).toBeInTheDocument();

      // Wait for categories to load
      await waitFor(() => {
        expect(screen.queryByTestId('loading-spinner')).not.toBeInTheDocument();
      });

      // Should display the categories
      expect(screen.getByText('Food & Dining')).toBeInTheDocument();
      expect(screen.getByText('Restaurant and food purchases')).toBeInTheDocument();
      expect(screen.getByText('Transportation')).toBeInTheDocument();
      expect(screen.getByText('Gas and car expenses')).toBeInTheDocument();

      // Verify fetch was called correctly
      expect(global.fetch).toHaveBeenCalledWith('/api/categories/get');
    });

    test('displays empty state when no categories exist', async () => {
      // Mock empty response
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ categories: [] }),
      });

      render(<CategoryManager />);

      await waitFor(() => {
        expect(screen.queryByTestId('loading-spinner')).not.toBeInTheDocument();
      });

      expect(screen.getByText('No categories created yet. Create your first category above!')).toBeInTheDocument();
    });

    test('displays error message when fetch fails', async () => {
      // Mock failed fetch response
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: false,
        status: 500,
      });

      render(<CategoryManager />);

      await waitFor(() => {
        expect(screen.queryByTestId('loading-spinner')).not.toBeInTheDocument();
      });

      expect(screen.getByText('Failed to fetch categories')).toBeInTheDocument();
    });
  });

  // Test Case 2: Create a New Category
  describe('Create a New Category', () => {
    test('creates a new category successfully', async () => {
      // Mock initial fetch for categories
      (global.fetch as jest.Mock)
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({ categories: [] }),
        })
        // Mock create category response
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({ success: true }),
        })
        // Mock refetch categories after creation
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({ categories: [mockCategories[0]] }),
        });

      render(<CategoryManager />);

      // Wait for initial load
      await waitFor(() => {
        expect(screen.queryByTestId('loading-spinner')).not.toBeInTheDocument();
      });

      // Find and click the create button
      const createButton = screen.getByRole('button', { name: /create category/i });
      fireEvent.click(createButton);

      // Wait for the API calls to complete
      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalledWith('/api/categories/create', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            name: 'Test Category',
            description: 'Test Description',
            color: '#ef4444',
            icon: '🍔',
          }),
        });
      });

      // Should refetch categories after creation
      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalledTimes(3); // Initial fetch + create + refetch
      });
    });

    test('displays error when create fails', async () => {
      // Mock initial fetch for categories
      (global.fetch as jest.Mock)
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({ categories: [] }),
        })
        // Mock failed create response
        .mockResolvedValueOnce({
          ok: false,
          json: async () => ({ error: 'Category name already exists' }),
        });

      render(<CategoryManager />);

      await waitFor(() => {
        expect(screen.queryByTestId('loading-spinner')).not.toBeInTheDocument();
      });

      const createButton = screen.getByRole('button', { name: /create category/i });
      fireEvent.click(createButton);

      await waitFor(() => {
        expect(screen.getByText('Category name already exists')).toBeInTheDocument();
      });
    });
  });

  // Test Case 3: Edit an Existing Category
  describe('Edit an Existing Category', () => {
    test('populates form with category data when edit is clicked', async () => {
      // Mock initial fetch with categories
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ categories: mockCategories }),
      });

      render(<CategoryManager />);

      await waitFor(() => {
        expect(screen.queryByTestId('loading-spinner')).not.toBeInTheDocument();
      });

      // Click edit button for first category
      const editButtons = screen.getAllByText('Edit');
      fireEvent.click(editButtons[0]);

      // Should show "Edit Category" title
      expect(screen.getByText('Edit Category')).toBeInTheDocument();
      expect(screen.getByText('Update the details of your category')).toBeInTheDocument();

      // Should show "Update Category" button instead of "Create Category"
      expect(screen.getByRole('button', { name: /update category/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /cancel/i })).toBeInTheDocument();
    });

    test('updates category successfully', async () => {
      // Mock initial fetch with categories
      (global.fetch as jest.Mock)
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({ categories: mockCategories }),
        })
        // Mock update response
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({ success: true }),
        })
        // Mock refetch after update
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({ categories: mockCategories }),
        });

      render(<CategoryManager />);

      await waitFor(() => {
        expect(screen.queryByTestId('loading-spinner')).not.toBeInTheDocument();
      });

      // Click edit button
      const editButtons = screen.getAllByText('Edit');
      fireEvent.click(editButtons[0]);

      // Click update button
      const updateButton = screen.getByRole('button', { name: /update category/i });
      fireEvent.click(updateButton);

      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalledWith('/api/categories/update', {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            category_id: 'cat-1',
            name: 'Test Category',
            description: 'Test Description',
            color: '#ef4444',
            icon: '🍔',
          }),
        });
      });
    });

    test('cancels editing and resets form', async () => {
      // Mock initial fetch with categories
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ categories: mockCategories }),
      });

      render(<CategoryManager />);

      await waitFor(() => {
        expect(screen.queryByTestId('loading-spinner')).not.toBeInTheDocument();
      });

      // Click edit button
      const editButtons = screen.getAllByText('Edit');
      fireEvent.click(editButtons[0]);

      // Should be in edit mode
      expect(screen.getByText('Edit Category')).toBeInTheDocument();

      // Click cancel button
      const cancelButton = screen.getByRole('button', { name: /cancel/i });
      fireEvent.click(cancelButton);

      // Should return to create mode
      expect(screen.getByText('Create New Category')).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /create category/i })).toBeInTheDocument();
    });
  });

  // Test Case 4: Delete a Category
  describe('Delete a Category', () => {
    test('deletes category successfully when confirmed', async () => {
      // Mock initial fetch with categories
      (global.fetch as jest.Mock)
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({ categories: mockCategories }),
        })
        // Mock delete response
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({ success: true }),
        })
        // Mock refetch after delete
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({ categories: [mockCategories[1]] }),
        });

      render(<CategoryManager />);

      await waitFor(() => {
        expect(screen.queryByTestId('loading-spinner')).not.toBeInTheDocument();
      });

      // Click delete button for first category
      const deleteButtons = screen.getAllByText('Delete');
      fireEvent.click(deleteButtons[0]);

      // Should call confirm
      expect(global.confirm).toHaveBeenCalledWith('Are you sure you want to delete "Food & Dining"?');

      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalledWith('/api/categories/delete', {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ category_id: 'cat-1' }),
        });
      });

      // Should refetch categories after deletion
      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalledTimes(3); // Initial fetch + delete + refetch
      });
    });

    test('does not delete category when not confirmed', async () => {
      // Mock confirm to return false
      (global.confirm as jest.Mock).mockReturnValue(false);

      // Mock initial fetch with categories
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ categories: mockCategories }),
      });

      render(<CategoryManager />);

      await waitFor(() => {
        expect(screen.queryByTestId('loading-spinner')).not.toBeInTheDocument();
      });

      // Click delete button
      const deleteButtons = screen.getAllByText('Delete');
      fireEvent.click(deleteButtons[0]);

      // Should call confirm but not proceed with deletion
      expect(global.confirm).toHaveBeenCalledWith('Are you sure you want to delete "Food & Dining"?');
      expect(global.fetch).toHaveBeenCalledTimes(1); // Only initial fetch
    });

    test('displays error when delete fails', async () => {
      // Mock initial fetch with categories
      (global.fetch as jest.Mock)
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({ categories: mockCategories }),
        })
        // Mock failed delete response
        .mockResolvedValueOnce({
          ok: false,
          json: async () => ({ error: 'Category is in use and cannot be deleted' }),
        });

      render(<CategoryManager />);

      await waitFor(() => {
        expect(screen.queryByTestId('loading-spinner')).not.toBeInTheDocument();
      });

      // Click delete button
      const deleteButtons = screen.getAllByText('Delete');
      fireEvent.click(deleteButtons[0]);

      await waitFor(() => {
        expect(screen.getByText('Category is in use and cannot be deleted')).toBeInTheDocument();
      });
    });
  });

  // Test Case 5: Handle API Errors
  describe('Handle API Errors', () => {
    test('handles network error during fetch', async () => {
      // Mock network error
      (global.fetch as jest.Mock).mockRejectedValueOnce(new Error('Network error'));

      render(<CategoryManager />);

      await waitFor(() => {
        expect(screen.queryByTestId('loading-spinner')).not.toBeInTheDocument();
      });

      expect(screen.getByText('Network error')).toBeInTheDocument();
    });

    test('handles error during category creation', async () => {
      // Mock initial fetch
      (global.fetch as jest.Mock)
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({ categories: [] }),
        })
        // Mock network error during creation
        .mockRejectedValueOnce(new Error('Failed to save category'));

      render(<CategoryManager />);

      await waitFor(() => {
        expect(screen.queryByTestId('loading-spinner')).not.toBeInTheDocument();
      });

      const createButton = screen.getByRole('button', { name: /create category/i });
      fireEvent.click(createButton);

      await waitFor(() => {
        expect(screen.getByText('Failed to save category')).toBeInTheDocument();
      });
    });

    test('handles error during category update', async () => {
      // Mock initial fetch
      (global.fetch as jest.Mock)
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({ categories: mockCategories }),
        })
        // Mock network error during update
        .mockRejectedValueOnce(new Error('Failed to save category'));

      render(<CategoryManager />);

      await waitFor(() => {
        expect(screen.queryByTestId('loading-spinner')).not.toBeInTheDocument();
      });

      // Enter edit mode
      const editButtons = screen.getAllByText('Edit');
      fireEvent.click(editButtons[0]);

      // Try to update
      const updateButton = screen.getByRole('button', { name: /update category/i });
      fireEvent.click(updateButton);

      await waitFor(() => {
        expect(screen.getByText('Failed to save category')).toBeInTheDocument();
      });
    });

    test('handles error during category deletion', async () => {
      // Mock initial fetch
      (global.fetch as jest.Mock)
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({ categories: mockCategories }),
        })
        // Mock network error during deletion
        .mockRejectedValueOnce(new Error('Failed to delete category'));

      render(<CategoryManager />);

      await waitFor(() => {
        expect(screen.queryByTestId('loading-spinner')).not.toBeInTheDocument();
      });

      // Try to delete
      const deleteButtons = screen.getAllByText('Delete');
      fireEvent.click(deleteButtons[0]);

      await waitFor(() => {
        expect(screen.getByText('Failed to delete category')).toBeInTheDocument();
      });
    });
  });

  // Additional test for UI elements
  describe('UI Elements and Interactions', () => {
    test('displays category icons and colors correctly', async () => {
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ categories: mockCategories }),
      });

      render(<CategoryManager />);

      await waitFor(() => {
        expect(screen.queryByTestId('loading-spinner')).not.toBeInTheDocument();
      });

      // Check that category icons are displayed in the category list
      expect(screen.getAllByText('🍔')).toHaveLength(2); // One in form, one in category list
      expect(screen.getAllByText('🚗')).toHaveLength(2); // One in form, one in category list
    });

    test('shows form validation messages', async () => {
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ categories: [] }),
      });

      render(<CategoryManager />);

      await waitFor(() => {
        expect(screen.queryByTestId('loading-spinner')).not.toBeInTheDocument();
      });

      // Form fields should be present
      expect(screen.getByPlaceholderText('e.g., Coffee Shops')).toBeInTheDocument();
      expect(screen.getByPlaceholderText('e.g., Daily coffee purchases')).toBeInTheDocument();
      expect(screen.getByPlaceholderText('Or enter custom emoji')).toBeInTheDocument();
    });
  });
});