import React from 'react';
import { render, screen } from '@testing-library/react';
import AccountCard from '../AccountCard';

const mockAccount = {
  id: '1',
  account_name: 'Checking Account',
  institution_name: 'Bank A',
  current_balance: 1000,
  last_synced_at: '2025-06-01T12:00:00Z',
  plaid_item_id: 'item_1',
};

describe('AccountCard', () => {
  test('renders account details correctly', () => {
    render(<AccountCard account={mockAccount} />);

    expect(screen.getByText('Checking Account')).toBeInTheDocument();
    expect(screen.getByText(/Institution: Bank A/)).toBeInTheDocument();
    expect(screen.getByText(/Current Balance:/)).toBeInTheDocument();
  });
});
