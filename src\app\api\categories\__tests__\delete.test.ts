import { DELETE } from '../delete/route';
import { createSupabaseServerClient } from '@/lib/supabase/server';
import { NextRequest } from 'next/server';

// Mock dependencies
jest.mock('@/lib/supabase/server', () => ({
  createSupabaseServerClient: jest.fn(),
}));

jest.mock('next/server', () => ({
  ...jest.requireActual('next/server'),
  NextResponse: {
    json: jest.fn((body, init) => ({
      status: init?.status || 200,
      json: () => Promise.resolve(body),
    })),
  },
}));

describe('DELETE /api/categories/delete', () => {
  const mockUser = { id: 'test-user-id' };
  const mockCategoryId = 'cat-123';

  const createMockRequest = (body: Record<string, unknown>) => {
    return new Request('http://localhost/api/categories/delete', {
      method: 'DELETE',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(body),
    }) as NextRequest;
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('Success (200 OK) - deletes category successfully', async () => {
    const mockSupabase = {
      auth: {
        getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
      },
      from: jest.fn((tableName: string) => {
        if (tableName === 'user_categories') {
          return {
            select: jest.fn().mockReturnValue({
              eq: jest.fn().mockReturnValue({
                eq: jest.fn().mockReturnValue({
                  single: jest.fn().mockResolvedValue({
                    data: { id: mockCategoryId, name: 'Test Category' },
                    error: null,
                  }),
                }),
              }),
            }),
            delete: jest.fn().mockReturnValue({
              eq: jest.fn().mockReturnValue({
                eq: jest.fn().mockResolvedValue({ error: null }),
              }),
            }),
          };
        }
        if (tableName === 'transactions') {
          return {
            select: jest.fn().mockReturnValue({
              eq: jest.fn().mockReturnValue({
                eq: jest.fn().mockReturnValue({
                  select: jest.fn().mockResolvedValue({ count: 0, error: null }),
                }),
              }),
            }),
          };
        }
        // A more robust implementation of the mock
        const fromReturn = {
          select: jest.fn().mockReturnThis(),
          delete: jest.fn().mockReturnThis(),
          eq: jest.fn().mockReturnThis(),
          single: jest.fn(),
        };

        // Specific mock for ownership check
        if (tableName === 'user_categories') {
          fromReturn.single.mockResolvedValueOnce({
            data: { id: mockCategoryId, name: 'Test Category' },
            error: null,
          });
        }

        // Specific mock for usage check
        if (tableName === 'transactions') {
          // This is a simplified mock. A real scenario might need more specific chaining.
          // For the purpose of this test, we assume the count is returned from a chained call.
          // A more accurate mock would reflect the actual calls, like:
          // .select('*', { count: 'exact', head: true })
          // We'll mock the final result of the chain.
          jest.spyOn(fromReturn, 'select').mockImplementation((columns, options) => {
            if (options?.count === 'exact') {
              return Promise.resolve({ count: 0, error: null });
            }
            return Promise.resolve({ data: [], error: null });
          });
        }

        // Specific mock for delete operation
        if (tableName === 'user_categories') {
          fromReturn.delete().eq().eq.mockResolvedValueOnce({ error: null });
        }

        return fromReturn;
      }),
    };

    (createSupabaseServerClient as jest.Mock).mockResolvedValue(mockSupabase);

    const req = createMockRequest({ category_id: mockCategoryId });
    const res = await DELETE(req);
    const body = await res.json();

    expect(res.status).toBe(200);
    expect(body).toEqual({
      message: 'Category deleted successfully',
      deleted_category: {
        id: mockCategoryId,
        name: 'Test Category',
      },
    });
    expect(mockSupabase.auth.getUser).toHaveBeenCalledTimes(1);
    expect(mockSupabase.from).toHaveBeenCalledWith('user_categories');
    expect(mockSupabase.from).toHaveBeenCalledWith('transactions');
  });

  test('Unauthorized (401) - no user authenticated', async () => {
    const mockSupabase = {
      auth: {
        getUser: jest
          .fn()
          .mockResolvedValue({ data: { user: null }, error: { message: 'Unauthorized' } }),
      },
    };

    (createSupabaseServerClient as jest.Mock).mockResolvedValue(mockSupabase);

    const req = createMockRequest({ category_id: mockCategoryId });
    const res = await DELETE(req);
    const body = await res.json();

    expect(res.status).toBe(401);
    expect(body).toEqual({ error: 'Unauthorized' });
  });

  test('Category Not Found or No Access (404)', async () => {
    const mockSupabase = {
      auth: {
        getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
      },
      from: jest.fn().mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            eq: jest.fn().mockReturnValue({
              single: jest.fn().mockResolvedValue({ data: null, error: { message: 'Not found' } }),
            }),
          }),
        }),
      }),
    };

    (createSupabaseServerClient as jest.Mock).mockResolvedValue(mockSupabase);

    const req = createMockRequest({ category_id: 'cat-non-existent' });
    const res = await DELETE(req);
    const body = await res.json();

    expect(res.status).toBe(404);
    expect(body).toEqual({ error: 'Category not found or access denied' });
  });

  test('Category In Use (409 Conflict)', async () => {
    const mockSupabase = {
      auth: {
        getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
      },
      from: jest.fn((tableName: string) => {
        if (tableName === 'user_categories') {
          return {
            select: jest.fn().mockReturnValue({
              eq: jest.fn().mockReturnValue({
                eq: jest.fn().mockReturnValue({
                  single: jest
                    .fn()
                    .mockResolvedValue({
                      data: { id: mockCategoryId, name: 'In-Use Category' },
                      error: null,
                    }),
                }),
              }),
            }),
          };
        }
        if (tableName === 'transactions') {
          return {
            select: jest.fn((_selector, options) => {
              if (options && options.count === 'exact') {
                return {
                  eq: jest.fn().mockReturnThis(),
                  // Mock the final chained call that returns the count
                  // This is a simplification. A more complex mock might be needed
                  // if there are more chained methods after eq.
                  // For this test, we assume `eq` is the last in the chain before the promise resolves.
                  then: (callback: (result: { count: number; error: null }) => void) =>
                    callback({ count: 2, error: null }),
                };
              }
              return {
                eq: jest.fn().mockReturnThis(),
                then: (callback: (result: { data: never[]; error: null }) => void) =>
                  callback({ data: [], error: null }),
              };
            }),
          };
        }
        return {};
      }),
    };

    (createSupabaseServerClient as jest.Mock).mockResolvedValue(mockSupabase);

    const req = createMockRequest({ category_id: mockCategoryId });
    const res = await DELETE(req);
    const body = await res.json();

    expect(res.status).toBe(409);
    expect(body.error).toContain('Cannot delete category that is assigned to transactions');
  });

  test('Database Delete Error (500)', async () => {
    const mockSupabase = {
      auth: {
        getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
      },
      from: jest.fn((tableName: string) => {
        if (tableName === 'user_categories') {
          return {
            select: jest.fn().mockReturnValue({
              eq: jest.fn().mockReturnValue({
                eq: jest.fn().mockReturnValue({
                  single: jest
                    .fn()
                    .mockResolvedValue({
                      data: { id: mockCategoryId, name: 'Test Category' },
                      error: null,
                    }),
                }),
              }),
            }),
            delete: jest.fn().mockReturnValue({
              eq: jest.fn().mockReturnValue({
                eq: jest.fn().mockResolvedValue({ error: { message: 'DB error' } }),
              }),
            }),
          };
        }
        if (tableName === 'transactions') {
          return {
            select: jest.fn().mockReturnValue({
              eq: jest.fn().mockReturnValue({
                eq: jest.fn().mockReturnValue({
                  select: jest.fn().mockResolvedValue({ count: 0, error: null }),
                }),
              }),
            }),
          };
        }
        return {};
      }),
    };

    (createSupabaseServerClient as jest.Mock).mockResolvedValue(mockSupabase);

    const req = createMockRequest({ category_id: mockCategoryId });
    const res = await DELETE(req);
    const body = await res.json();

    expect(res.status).toBe(500);
    expect(body).toEqual({ error: 'Failed to delete category' });
  });
});
