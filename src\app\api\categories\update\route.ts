import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClient } from '@/lib/supabase/server';

export async function PUT(request: NextRequest) {
  const supabase = await createSupabaseServerClient();

  // Check authentication
  const {
    data: { user },
    error,
  } = await supabase.auth.getUser();

  if (error || !user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  const userId = user.id;

  try {
    // Parse request body
    const body = await request.json();
    const { category_id, name, color, icon, description } = body;

    // Validate required fields
    if (!category_id || typeof category_id !== 'string') {
      return NextResponse.json(
        { error: 'Category ID is required and must be a string' },
        { status: 400 }
      );
    }

    // Validate that at least one field is being updated
    if (!name && !color && !icon && description === undefined) {
      return NextResponse.json(
        { error: 'At least one field must be provided for update' },
        { status: 400 }
      );
    }

    // Validate field types
    if (name !== undefined && (typeof name !== 'string' || name.trim().length === 0)) {
      return NextResponse.json({ error: 'Name must be a non-empty string' }, { status: 400 });
    }

    if (color !== undefined && color !== null && typeof color !== 'string') {
      return NextResponse.json({ error: 'Color must be a string or null' }, { status: 400 });
    }

    if (icon !== undefined && icon !== null && typeof icon !== 'string') {
      return NextResponse.json({ error: 'Icon must be a string or null' }, { status: 400 });
    }

    if (description !== undefined && description !== null && typeof description !== 'string') {
      return NextResponse.json({ error: 'Description must be a string or null' }, { status: 400 });
    }

    // First, verify the category exists and belongs to the user
    const { data: existingCategory, error: fetchError } = await supabase
      .from('user_categories')
      .select('id')
      .eq('id', category_id)
      .eq('user_id', userId)
      .single();

    if (fetchError || !existingCategory) {
      return NextResponse.json({ error: 'Category not found or access denied' }, { status: 404 });
    }

    // Build update object with only provided fields
    const updateData: {
      name?: string;
      color?: string | null;
      icon?: string | null;
      description?: string | null;
    } = {};
    if (name !== undefined) updateData.name = name.trim();
    if (color !== undefined) updateData.color = color;
    if (icon !== undefined) updateData.icon = icon;
    if (description !== undefined) updateData.description = description;

    // Update the category
    const { data: updatedCategory, error: updateError } = await supabase
      .from('user_categories')
      .update(updateData)
      .eq('id', category_id)
      .eq('user_id', userId)
      .select()
      .single();

    if (updateError) {
      console.error('Error updating category:', updateError);

      // Handle unique constraint violation
      if (updateError.code === '23505') {
        return NextResponse.json(
          { error: 'A category with this name already exists' },
          { status: 409 }
        );
      }

      return NextResponse.json({ error: 'Failed to update category' }, { status: 500 });
    }

    return NextResponse.json({ category: updatedCategory });
  } catch (error) {
    console.error('[CATEGORIES_UPDATE_ERROR]', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}
