import { z } from "zod";

// Base schema for server-side variables
const baseServerSchema = z.object({
  NODE_ENV: z.enum(["development", "production", "test"]).optional().default("development"),
  SUPABASE_SERVICE_ROLE_KEY: z.string().optional(),
  RESEND_API_KEY: z.string().optional(),
  SENDGRID_API_KEY: z.string().optional(),
  EMAIL_FROM_ADDRESS: z.string().email().optional(),
  EMAIL_FROM_NAME: z.string().optional(),
  NEXTAUTH_SECRET: z.string().optional(),
  APP_SIGNING_SECRET: z.string().optional(),
});

// Schema for Plaid variables, required by default
const plaidSchema = z.object({
  PLAID_CLIENT_ID: z.string().min(1),
  PLAID_SECRET_SANDBOX: z.string().min(1),
  PLAID_SECRET_DEVELOPMENT: z.string().min(1),
  PLAID_ENV: z.enum(["sandbox", "development", "production"]),
});

// Conditionally create the final server schema
const serverSchema = process.env.NODE_ENV === 'test'
  ? baseServerSchema.merge(plaidSchema.partial()) // In test, Plaid vars are optional
  : baseServerSchema.merge(plaidSchema); // In dev/prod, Plaid vars are required

const clientSchema = z.object({
  NEXT_PUBLIC_APP_URL: z.string().url().optional(),
  NEXT_PUBLIC_SUPABASE_URL: z.string().url(),
  NEXT_PUBLIC_SUPABASE_ANON_KEY: z.string().min(1),
  NEXT_PUBLIC_PLAID_OAUTH_REDIRECT_URI: z.string().url().optional(),
  NEXT_PUBLIC_ANALYTICS_ID: z.string().optional(),
});

const _env = {
  ...process.env,
};

let envServer, envClient;

try {
  envServer = serverSchema.parse(_env);

  const publicVars = {};
  for (const key in _env) {
    if (key.startsWith("NEXT_PUBLIC_")) {
      publicVars[key] = _env[key];
    }
  }
  envClient = clientSchema.parse(publicVars);

} catch (error) {
  if (error instanceof z.ZodError) {
    console.error("❌ Invalid environment variables:", error.flatten().fieldErrors);
    const fieldErrors = error.flatten().fieldErrors;
    let errorMessage = "Missing or invalid environment variables:\n";
    for (const key in fieldErrors) {
      errorMessage += `- ${key}: ${fieldErrors[key].join(", ")}\n`;
    }
    throw new Error(errorMessage);
  }
  throw error;
}

export const env = {
  ...envServer,
  ...envClient,
};
 