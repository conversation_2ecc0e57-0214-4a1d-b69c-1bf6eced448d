import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClient } from '@/lib/supabase/server';

export async function POST(request: NextRequest) {
  const supabase = await createSupabaseServerClient();

  // Check authentication
  const {
    data: { user },
    error,
  } = await supabase.auth.getUser();

  if (error || !user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  const userId = user.id;

  try {
    // Parse request body
    const body = await request.json();
    const { name, color, icon, description } = body;

    // Validate required fields
    if (!name || typeof name !== 'string' || name.trim().length === 0) {
      return NextResponse.json(
        { error: 'Name is required and must be a non-empty string' },
        { status: 400 }
      );
    }

    // Validate optional fields
    if (color && typeof color !== 'string') {
      return NextResponse.json(
        { error: 'Color must be a string' },
        { status: 400 }
      );
    }

    if (icon && typeof icon !== 'string') {
      return NextResponse.json(
        { error: 'Icon must be a string' },
        { status: 400 }
      );
    }

    if (description && typeof description !== 'string') {
      return NextResponse.json(
        { error: 'Description must be a string' },
        { status: 400 }
      );
    }

    // Insert new category
    const { data: category, error: insertError } = await supabase
      .from('user_categories')
      .insert({
        user_id: userId,
        name: name.trim(),
        color: color || null,
        icon: icon || null,
        description: description || null,
      })
      .select()
      .single();

    if (insertError) {
      console.error('Error creating category:', insertError);
      
      // Handle unique constraint violation
      if (insertError.code === '23505') {
        return NextResponse.json(
          { error: 'A category with this name already exists' },
          { status: 409 }
        );
      }
      
      return NextResponse.json(
        { error: 'Failed to create category' },
        { status: 500 }
      );
    }

    return NextResponse.json({ category }, { status: 201 });
  } catch (error) {
    console.error('[CATEGORIES_CREATE_ERROR]', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}