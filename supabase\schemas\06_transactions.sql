-- Transaction records with all Plaid fields
CREATE TABLE transactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    account_id UUID NOT NULL REFERENCES financial_accounts(id) ON DELETE CASCADE,
    plaid_transaction_id TEXT NOT NULL,
    
    -- Core transaction data
    amount DECIMAL(15,2) NOT NULL,
    currency_code TEXT DEFAULT 'USD',
    transaction_date DATE NOT NULL,
    authorized_date DATE,
    posted_date DATE,
    
    -- Transaction details
    merchant_name TEXT,
    description TEXT NOT NULL,
    category_id UUID REFERENCES transaction_categories(id),
    user_category_id UUID REFERENCES user_categories(id),
    
    -- Plaid-specific fields
    plaid_category JSONB, -- Plaid's category hierarchy
    plaid_category_detailed TEXT,
    account_owner TEXT,
    transaction_type TEXT,
    transaction_code TEXT,
    
    -- Location data
    location JSONB, -- Store address, city, region, postal_code, country, lat, lon
    
    -- Additional metadata
    is_pending BOOLEAN DEFAULT FALSE,
    is_recurring BOOLEAN DEFAULT FALSE,
    status TEXT NOT NULL DEFAULT 'posted' CHECK (status IN ('posted', 'pending', 'removed')),
    confidence_level TEXT,
    tags TEXT[], -- User-defined tags for organization
    notes TEXT, -- User notes
    
    -- System fields
    plaid_metadata JSONB, -- Store additional Plaid data
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    UNIQUE(user_id, plaid_transaction_id)
);

-- Indexes
CREATE INDEX idx_transactions_user_id ON transactions(user_id);
CREATE INDEX idx_transactions_account_id ON transactions(account_id);
CREATE INDEX idx_transactions_date ON transactions(transaction_date);
CREATE INDEX idx_transactions_amount ON transactions(amount);
CREATE INDEX idx_transactions_category_id ON transactions(category_id);
CREATE INDEX idx_transactions_user_category_id ON transactions(user_category_id);
CREATE INDEX idx_transactions_plaid_id ON transactions(plaid_transaction_id);
CREATE INDEX idx_transactions_merchant ON transactions(merchant_name);
CREATE INDEX idx_transactions_pending ON transactions(is_pending);
CREATE INDEX idx_transactions_status ON transactions(status);

-- Enable RLS
ALTER TABLE transactions ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Users can view own transactions" ON transactions
    FOR SELECT
    TO authenticated
    USING ((SELECT auth.uid()) = user_id);

CREATE POLICY "Users can insert own transactions" ON transactions
    FOR INSERT
    TO authenticated
    WITH CHECK ((SELECT auth.uid()) = user_id);

CREATE POLICY "Users can update own transactions" ON transactions
    FOR UPDATE
    TO authenticated
    USING ((SELECT auth.uid()) = user_id)
    WITH CHECK ((SELECT auth.uid()) = user_id);

CREATE POLICY "Users can delete own transactions" ON transactions
    FOR DELETE
    TO authenticated
    USING ((SELECT auth.uid()) = user_id);

-- Trigger for updated_at
CREATE TRIGGER update_transactions_updated_at BEFORE UPDATE ON transactions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Comments
COMMENT ON TABLE transactions IS 'Financial transaction records with complete Plaid data';
COMMENT ON COLUMN transactions.plaid_category IS 'Plaid category hierarchy as JSON array';
COMMENT ON COLUMN transactions.location IS 'Transaction location data including coordinates';
COMMENT ON COLUMN transactions.tags IS 'User-defined tags for transaction organization';
 