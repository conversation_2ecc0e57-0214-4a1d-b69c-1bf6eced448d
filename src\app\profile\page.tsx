'use client';

import React from 'react';
import UserProfile from '@/components/auth/UserProfile';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import { LogoutButton } from '@/components/auth/LogoutButton';
import Link from 'next/link';

export default function ProfilePage() {
  return (
    <ProtectedRoute>
      <div className='flex flex-col items-center justify-center min-h-screen p-4'>
        <h1 className='text-3xl font-semibold mb-6'>Your Profile</h1>
        <UserProfile />
        <div className='mt-6'>
          <LogoutButton />
        </div>
        <Link href='/dashboard' className='mt-4 text-blue-600 hover:underline'>
          Go to Dashboard
        </Link>
      </div>
    </ProtectedRoute>
  );
}
