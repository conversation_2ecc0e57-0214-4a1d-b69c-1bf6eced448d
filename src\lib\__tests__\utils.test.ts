import { cn } from '../utils';

// Test suite for the cn utility function
describe('cn', () => {
  // Test case 1: Merging simple string classes
  it('should merge string arguments', () => {
    expect(cn('hello', 'world')).toBe('hello world');
  });

  // Test case 2: Handling conditional classes with objects
  it('should handle conditional classes', () => {
    expect(cn('base', { 'is-active': true, 'is-hidden': false })).toBe('base is-active');
  });

  // Test case 3: Merging and overriding Tailwind CSS classes
  it('should correctly merge and override conflicting tailwind classes', () => {
    expect(cn('p-2', 'p-4')).toBe('p-4');
    expect(cn('m-2 p-2', 'm-8')).toBe('p-2 m-8');
  });

  // Test case 4: Handling various types of inputs
  it('should handle a mix of strings, objects, and arrays', () => {
    const isActive = true;
    const hasError = false;
    const classes = cn(
      'base-class',
      isActive && 'active-class',
      hasError ? 'error-class' : 'success-class',
      ['another-class', { 'yet-another': true }]
    );
    expect(classes).toBe('base-class active-class success-class another-class yet-another');
  });

  // Test case 5: Handling falsy values
  it('should ignore falsy values like null, undefined, and false', () => {
    expect(cn('a', null, 'b', undefined, false, 'c')).toBe('a b c');
  });
});
