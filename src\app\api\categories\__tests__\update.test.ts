import { PUT } from '../update/route';
import { createSupabaseServerClient } from '@/lib/supabase/server';
import { NextRequest } from 'next/server';

// Mock dependencies
jest.mock('@/lib/supabase/server', () => ({
  createSupabaseServerClient: jest.fn(),
}));

jest.mock('next/server', () => ({
  ...jest.requireActual('next/server'),
  NextResponse: {
    json: jest.fn((body, init) => ({
      status: init?.status || 200,
      json: () => Promise.resolve(body),
    })),
  },
}));

describe('PUT /api/categories/update', () => {
  const mockUser = { id: 'test-user-id' };
  const mockCategoryId = 'cat-123';
  const mockUpdateData = {
    category_id: mockCategoryId,
    name: 'Updated Category',
    color: '#00FF00',
    icon: 'updated-icon',
    description: 'Updated description',
  };

  const createMockRequest = (body: Record<string, unknown>) => {
    return new Request('http://localhost/api/categories/update', {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(body),
    }) as NextRequest;
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('Success (200 OK) - updates category with all fields', async () => {
    const mockUpdatedCategory = {
      id: mockCategoryId,
      user_id: mockUser.id,
      name: 'Updated Category',
      color: '#00FF00',
      icon: 'updated-icon',
      description: 'Updated description',
      is_active: true,
      created_at: '2025-06-08T20:00:00Z',
      updated_at: '2025-06-08T20:05:00Z',
    };

    const mockSupabase = {
      auth: {
        getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
      },
      from: jest.fn().mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            eq: jest.fn().mockReturnValue({
              single: jest.fn().mockResolvedValue({
                data: { id: mockCategoryId },
                error: null,
              }),
            }),
          }),
        }),
        update: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            eq: jest.fn().mockReturnValue({
              select: jest.fn().mockReturnValue({
                single: jest.fn().mockResolvedValue({
                  data: mockUpdatedCategory,
                  error: null,
                }),
              }),
            }),
          }),
        }),
      }),
    };

    (createSupabaseServerClient as jest.Mock).mockResolvedValue(mockSupabase);

    const req = createMockRequest(mockUpdateData);
    const res = await PUT(req);
    const body = await res.json();

    expect(res.status).toBe(200);
    expect(body).toEqual({ category: mockUpdatedCategory });
    expect(mockSupabase.auth.getUser).toHaveBeenCalledTimes(1);
    expect(mockSupabase.from).toHaveBeenCalledWith('user_categories');

    // Verify ownership check
    expect(mockSupabase.from().select).toHaveBeenCalledWith('id');

    // Verify update call
    expect(mockSupabase.from().update).toHaveBeenCalledWith({
      name: 'Updated Category',
      color: '#00FF00',
      icon: 'updated-icon',
      description: 'Updated description',
    });
  });

  test('Success (200 OK) - updates category with partial fields', async () => {
    const partialUpdateData = {
      category_id: mockCategoryId,
      name: 'Partially Updated Category',
    };

    const mockUpdatedCategory = {
      id: mockCategoryId,
      user_id: mockUser.id,
      name: 'Partially Updated Category',
      color: '#FF0000',
      icon: 'old-icon',
      description: 'Old description',
      is_active: true,
      created_at: '2025-06-08T20:00:00Z',
      updated_at: '2025-06-08T20:05:00Z',
    };

    const mockSupabase = {
      auth: {
        getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
      },
      from: jest.fn().mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            eq: jest.fn().mockReturnValue({
              single: jest.fn().mockResolvedValue({
                data: { id: mockCategoryId },
                error: null,
              }),
            }),
          }),
        }),
        update: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            eq: jest.fn().mockReturnValue({
              select: jest.fn().mockReturnValue({
                single: jest.fn().mockResolvedValue({
                  data: mockUpdatedCategory,
                  error: null,
                }),
              }),
            }),
          }),
        }),
      }),
    };

    (createSupabaseServerClient as jest.Mock).mockResolvedValue(mockSupabase);

    const req = createMockRequest(partialUpdateData);
    const res = await PUT(req);
    const body = await res.json();

    expect(res.status).toBe(200);
    expect(body).toEqual({ category: mockUpdatedCategory });
    expect(mockSupabase.from().update).toHaveBeenCalledWith({
      name: 'Partially Updated Category',
    });
  });

  test('Unauthorized (401) - no user authenticated', async () => {
    const mockSupabase = {
      auth: {
        getUser: jest
          .fn()
          .mockResolvedValue({ data: { user: null }, error: { message: 'Unauthorized' } }),
      },
    };

    (createSupabaseServerClient as jest.Mock).mockResolvedValue(mockSupabase);

    const req = createMockRequest(mockUpdateData);
    const res = await PUT(req);
    const body = await res.json();

    expect(res.status).toBe(401);
    expect(body).toEqual({ error: 'Unauthorized' });
    expect(mockSupabase.auth.getUser).toHaveBeenCalledTimes(1);
  });

  test('Unauthorized (401) - auth error', async () => {
    const mockSupabase = {
      auth: {
        getUser: jest.fn().mockResolvedValue({ data: { user: null }, error: null }),
      },
    };

    (createSupabaseServerClient as jest.Mock).mockResolvedValue(mockSupabase);

    const req = createMockRequest(mockUpdateData);
    const res = await PUT(req);
    const body = await res.json();

    expect(res.status).toBe(401);
    expect(body).toEqual({ error: 'Unauthorized' });
  });

  test('Category Not Found or No Access (404) - category does not exist', async () => {
    const mockSupabase = {
      auth: {
        getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
      },
      from: jest.fn().mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            eq: jest.fn().mockReturnValue({
              single: jest.fn().mockResolvedValue({
                data: null,
                error: { message: 'No rows returned' },
              }),
            }),
          }),
        }),
      }),
    };

    (createSupabaseServerClient as jest.Mock).mockResolvedValue(mockSupabase);

    const req = createMockRequest(mockUpdateData);
    const res = await PUT(req);
    const body = await res.json();

    expect(res.status).toBe(404);
    expect(body).toEqual({ error: 'Category not found or access denied' });
  });

  test('Category Not Found or No Access (404) - user does not own category', async () => {
    const mockSupabase = {
      auth: {
        getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
      },
      from: jest.fn().mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            eq: jest.fn().mockReturnValue({
              single: jest.fn().mockResolvedValue({
                data: null,
                error: null,
              }),
            }),
          }),
        }),
      }),
    };

    (createSupabaseServerClient as jest.Mock).mockResolvedValue(mockSupabase);

    const req = createMockRequest(mockUpdateData);
    const res = await PUT(req);
    const body = await res.json();

    expect(res.status).toBe(404);
    expect(body).toEqual({ error: 'Category not found or access denied' });
  });

  test('Missing category_id (400 Bad Request) - no category_id provided', async () => {
    const invalidData = {
      name: 'Updated Category',
      color: '#00FF00',
    };

    const mockSupabase = {
      auth: {
        getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
      },
    };

    (createSupabaseServerClient as jest.Mock).mockResolvedValue(mockSupabase);

    const req = createMockRequest(invalidData);
    const res = await PUT(req);
    const body = await res.json();

    expect(res.status).toBe(400);
    expect(body).toEqual({ error: 'Category ID is required and must be a string' });
  });

  test('Missing category_id (400 Bad Request) - category_id is not a string', async () => {
    const invalidData = {
      category_id: 123,
      name: 'Updated Category',
    };

    const mockSupabase = {
      auth: {
        getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
      },
    };

    (createSupabaseServerClient as jest.Mock).mockResolvedValue(mockSupabase);

    const req = createMockRequest(invalidData);
    const res = await PUT(req);
    const body = await res.json();

    expect(res.status).toBe(400);
    expect(body).toEqual({ error: 'Category ID is required and must be a string' });
  });

  test('No Fields to Update (400 Bad Request) - no update fields provided', async () => {
    const invalidData = {
      category_id: mockCategoryId,
    };

    const mockSupabase = {
      auth: {
        getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
      },
    };

    (createSupabaseServerClient as jest.Mock).mockResolvedValue(mockSupabase);

    const req = createMockRequest(invalidData);
    const res = await PUT(req);
    const body = await res.json();

    expect(res.status).toBe(400);
    expect(body).toEqual({ error: 'At least one field must be provided for update' });
  });

  test('Invalid Field Types (400 Bad Request) - empty name', async () => {
    const invalidData = {
      category_id: mockCategoryId,
      name: '',
      color: '#FF0000', // Add another field so it doesn't fail the "at least one field" check
    };

    const mockSupabase = {
      auth: {
        getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
      },
    };

    (createSupabaseServerClient as jest.Mock).mockResolvedValue(mockSupabase);

    const req = createMockRequest(invalidData);
    const res = await PUT(req);
    const body = await res.json();

    expect(res.status).toBe(400);
    expect(body).toEqual({ error: 'Name must be a non-empty string' });
  });

  test('Invalid Field Types (400 Bad Request) - whitespace only name', async () => {
    const invalidData = {
      category_id: mockCategoryId,
      name: '   ',
      color: '#FF0000', // Add another field so it doesn't fail the "at least one field" check
    };

    const mockSupabase = {
      auth: {
        getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
      },
    };

    (createSupabaseServerClient as jest.Mock).mockResolvedValue(mockSupabase);

    const req = createMockRequest(invalidData);
    const res = await PUT(req);
    const body = await res.json();

    expect(res.status).toBe(400);
    expect(body).toEqual({ error: 'Name must be a non-empty string' });
  });

  test('Invalid Field Types (400 Bad Request) - non-string color', async () => {
    const invalidData = {
      category_id: mockCategoryId,
      name: 'Updated Category',
      color: 123,
    };

    const mockSupabase = {
      auth: {
        getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
      },
    };

    (createSupabaseServerClient as jest.Mock).mockResolvedValue(mockSupabase);

    const req = createMockRequest(invalidData);
    const res = await PUT(req);
    const body = await res.json();

    expect(res.status).toBe(400);
    expect(body).toEqual({ error: 'Color must be a string or null' });
  });

  test('Invalid Field Types (400 Bad Request) - non-string icon', async () => {
    const invalidData = {
      category_id: mockCategoryId,
      name: 'Updated Category',
      icon: 456,
    };

    const mockSupabase = {
      auth: {
        getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
      },
    };

    (createSupabaseServerClient as jest.Mock).mockResolvedValue(mockSupabase);

    const req = createMockRequest(invalidData);
    const res = await PUT(req);
    const body = await res.json();

    expect(res.status).toBe(400);
    expect(body).toEqual({ error: 'Icon must be a string or null' });
  });

  test('Invalid Field Types (400 Bad Request) - non-string description', async () => {
    const invalidData = {
      category_id: mockCategoryId,
      name: 'Updated Category',
      description: 789,
    };

    const mockSupabase = {
      auth: {
        getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
      },
    };

    (createSupabaseServerClient as jest.Mock).mockResolvedValue(mockSupabase);

    const req = createMockRequest(invalidData);
    const res = await PUT(req);
    const body = await res.json();

    expect(res.status).toBe(400);
    expect(body).toEqual({ error: 'Description must be a string or null' });
  });

  test('Database Update Error (500) - general database error', async () => {
    const mockSupabase = {
      auth: {
        getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
      },
      from: jest.fn().mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            eq: jest.fn().mockReturnValue({
              single: jest.fn().mockResolvedValue({
                data: { id: mockCategoryId },
                error: null,
              }),
            }),
          }),
        }),
        update: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            eq: jest.fn().mockReturnValue({
              select: jest.fn().mockReturnValue({
                single: jest.fn().mockResolvedValue({
                  data: null,
                  error: {
                    code: '42000',
                    message: 'Database connection failed',
                  },
                }),
              }),
            }),
          }),
        }),
      }),
    };

    (createSupabaseServerClient as jest.Mock).mockResolvedValue(mockSupabase);

    const req = createMockRequest(mockUpdateData);
    const res = await PUT(req);
    const body = await res.json();

    expect(res.status).toBe(500);
    expect(body).toEqual({ error: 'Failed to update category' });
  });

  test('Duplicate Name Error (409 Conflict) - category name already exists', async () => {
    const mockSupabase = {
      auth: {
        getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
      },
      from: jest.fn().mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            eq: jest.fn().mockReturnValue({
              single: jest.fn().mockResolvedValue({
                data: { id: mockCategoryId },
                error: null,
              }),
            }),
          }),
        }),
        update: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            eq: jest.fn().mockReturnValue({
              select: jest.fn().mockReturnValue({
                single: jest.fn().mockResolvedValue({
                  data: null,
                  error: {
                    code: '23505',
                    message: 'duplicate key value violates unique constraint',
                  },
                }),
              }),
            }),
          }),
        }),
      }),
    };

    (createSupabaseServerClient as jest.Mock).mockResolvedValue(mockSupabase);

    const req = createMockRequest(mockUpdateData);
    const res = await PUT(req);
    const body = await res.json();

    expect(res.status).toBe(409);
    expect(body).toEqual({ error: 'A category with this name already exists' });
  });

  test('JSON Parse Error (500) - invalid JSON in request body', async () => {
    const mockSupabase = {
      auth: {
        getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
      },
    };

    (createSupabaseServerClient as jest.Mock).mockResolvedValue(mockSupabase);

    // Create a request with invalid JSON
    const req = new Request('http://localhost/api/categories/update', {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: 'invalid json{',
    }) as NextRequest;

    const res = await PUT(req);
    const body = await res.json();

    expect(res.status).toBe(500);
    expect(body).toEqual({ error: 'Internal Server Error' });
  });

  test('Name trimming - trims whitespace from name', async () => {
    const dataWithWhitespace = {
      category_id: mockCategoryId,
      name: '  Updated Category  ',
      color: '#00FF00',
    };

    const mockUpdatedCategory = {
      id: mockCategoryId,
      user_id: mockUser.id,
      name: 'Updated Category',
      color: '#00FF00',
      icon: 'old-icon',
      description: 'old description',
      is_active: true,
      created_at: '2025-06-08T20:00:00Z',
      updated_at: '2025-06-08T20:05:00Z',
    };

    const mockSupabase = {
      auth: {
        getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
      },
      from: jest.fn().mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            eq: jest.fn().mockReturnValue({
              single: jest.fn().mockResolvedValue({
                data: { id: mockCategoryId },
                error: null,
              }),
            }),
          }),
        }),
        update: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            eq: jest.fn().mockReturnValue({
              select: jest.fn().mockReturnValue({
                single: jest.fn().mockResolvedValue({
                  data: mockUpdatedCategory,
                  error: null,
                }),
              }),
            }),
          }),
        }),
      }),
    };

    (createSupabaseServerClient as jest.Mock).mockResolvedValue(mockSupabase);

    const req = createMockRequest(dataWithWhitespace);
    const res = await PUT(req);
    const body = await res.json();

    expect(res.status).toBe(200);
    expect(body).toEqual({ category: mockUpdatedCategory });
    expect(mockSupabase.from().update).toHaveBeenCalledWith({
      name: 'Updated Category', // Should be trimmed
      color: '#00FF00',
    });
  });

  test('Null values handling - allows null for optional fields', async () => {
    const dataWithNulls = {
      category_id: mockCategoryId,
      name: 'Updated Category',
      color: null,
      icon: null,
      description: null,
    };

    const mockUpdatedCategory = {
      id: mockCategoryId,
      user_id: mockUser.id,
      name: 'Updated Category',
      color: null,
      icon: null,
      description: null,
      is_active: true,
      created_at: '2025-06-08T20:00:00Z',
      updated_at: '2025-06-08T20:05:00Z',
    };

    const mockSupabase = {
      auth: {
        getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
      },
      from: jest.fn().mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            eq: jest.fn().mockReturnValue({
              single: jest.fn().mockResolvedValue({
                data: { id: mockCategoryId },
                error: null,
              }),
            }),
          }),
        }),
        update: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            eq: jest.fn().mockReturnValue({
              select: jest.fn().mockReturnValue({
                single: jest.fn().mockResolvedValue({
                  data: mockUpdatedCategory,
                  error: null,
                }),
              }),
            }),
          }),
        }),
      }),
    };

    (createSupabaseServerClient as jest.Mock).mockResolvedValue(mockSupabase);

    const req = createMockRequest(dataWithNulls);
    const res = await PUT(req);
    const body = await res.json();

    expect(res.status).toBe(200);
    expect(body).toEqual({ category: mockUpdatedCategory });
    expect(mockSupabase.from().update).toHaveBeenCalledWith({
      name: 'Updated Category',
      color: null,
      icon: null,
      description: null,
    });
  });
});
