'use client';

import React, { useState, useEffect } from 'react';
import TransactionCard from '@/components/transactions/TransactionCard';
import LoadingSpinner from '@/components/ui/LoadingSpinner';

interface Transaction {
  id: string;
  amount: number;
  currency_code: string;
  transaction_date: string;
  authorized_date: string | null;
  posted_date: string | null;
  merchant_name: string | null;
  description: string;
  category_id: string | null;
  user_category_id: string | null;
  plaid_category: string[] | null;
  plaid_category_detailed: string[] | null;
  transaction_type: string;
  location: Record<string, unknown> | null;
  is_pending: boolean;
  is_recurring: boolean;
  status: string;
  tags: string[] | null;
  notes: string | null;
  created_at: string;
  updated_at: string;
  financial_accounts: {
    id: string;
    account_name: string;
    institution_name: string;
    account_type: string;
    account_subtype: string;
    mask: string | null;
  };
}

interface PaginationInfo {
  currentPage: number;
  pageSize: number;
  totalCount: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

interface TransactionsResponse {
  transactions: Transaction[];
  pagination: PaginationInfo;
}

/**
 * TransactionsList component fetches and displays a paginated list of user transactions.
 * It handles loading state, error state, and pagination controls.
 *
 * @component
 * @returns {JSX.Element} The rendered list of transactions with pagination.
 */
export default function TransactionsList() {
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [pagination, setPagination] = useState<PaginationInfo | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);

  const fetchTransactions = async (page: number = 1) => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/transactions/get?page=${page}&pageSize=20`);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch transactions');
      }

      const data: TransactionsResponse = await response.json();
      setTransactions(data.transactions);
      setPagination(data.pagination);
      setCurrentPage(page);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchTransactions(1);
  }, []);

  const handlePageChange = (newPage: number) => {
    if (pagination && newPage >= 1 && newPage <= pagination.totalPages) {
      fetchTransactions(newPage);
    }
  };

  if (isLoading) {
    return (
      <div className='flex flex-col items-center justify-center py-12'>
        <LoadingSpinner className='mb-4' />
        <p className='text-gray-600'>Loading transactions...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className='flex flex-col items-center justify-center py-12'>
        <div className='bg-red-50 border border-red-200 rounded-lg p-6 max-w-md'>
          <h3 className='text-red-800 font-semibold mb-2'>Error Loading Transactions</h3>
          <p className='text-red-600 mb-4'>{error}</p>
          <button
            onClick={() => fetchTransactions(currentPage)}
            className='px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700'
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  if (transactions.length === 0) {
    return (
      <div className='flex flex-col items-center justify-center py-12'>
        <div className='bg-gray-50 border border-gray-200 rounded-lg p-6 max-w-md text-center'>
          <h3 className='text-gray-800 font-semibold mb-2'>No Transactions Found</h3>
          <p className='text-gray-600'>
            You don&apos;t have any transactions yet. Connect your bank accounts to start importing
            transactions.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className='space-y-6'>
      {/* Transactions List */}
      <div className='space-y-4'>
        {transactions.map((transaction) => (
          <TransactionCard key={transaction.id} transaction={transaction} />
        ))}
      </div>

      {/* Pagination */}
      {pagination && pagination.totalPages > 1 && (
        <div className='flex items-center justify-between border-t pt-6'>
          <div className='text-sm text-gray-600'>
            Showing {(pagination.currentPage - 1) * pagination.pageSize + 1} to{' '}
            {Math.min(pagination.currentPage * pagination.pageSize, pagination.totalCount)} of{' '}
            {pagination.totalCount} transactions
          </div>

          <div className='flex items-center space-x-2'>
            <button
              onClick={() => handlePageChange(pagination.currentPage - 1)}
              disabled={!pagination.hasPreviousPage}
              className='px-3 py-2 text-sm border rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed'
            >
              Previous
            </button>

            <span className='px-3 py-2 text-sm'>
              Page {pagination.currentPage} of {pagination.totalPages}
            </span>

            <button
              onClick={() => handlePageChange(pagination.currentPage + 1)}
              disabled={!pagination.hasNextPage}
              className='px-3 py-2 text-sm border rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed'
            >
              Next
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
