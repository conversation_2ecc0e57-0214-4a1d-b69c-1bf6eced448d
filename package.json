{"name": "navsync", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit --pretty", "type-check:project": "tsc --noEmit --pretty --project tsconfig.json", "audit": "npm audit --audit-level=moderate", "prepare": "husky", "format": "prettier --write . --ignore-path .gitignore", "test": "jest", "test:watch": "jest --watch", "test:ci": "jest --ci"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@radix-ui/react-slot": "^1.2.3", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.49.8", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "lucide-react": "^0.511.0", "next": "^15.3.2", "plaid": "^35.0.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.4", "react-plaid-link": "^4.0.1", "tailwind-merge": "^3.3.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.25.42"}, "devDependencies": {"@eslint/eslintrc": "^3", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^29.5.14", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.1.8", "eslint-config-prettier": "^10.1.5", "husky": "^9.1.7", "jest": "^29.7.0", "jest-environment-jsdom": "^30.0.0-beta.3", "lint-staged": "^16.0.0", "postcss": "^8", "prettier": "^3.5.3", "tailwindcss": "^3.4.1", "typescript": "^5"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,md,css,html,yml,yaml}": ["prettier --write"]}}