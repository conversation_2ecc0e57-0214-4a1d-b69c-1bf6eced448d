'use client';

import * as React from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useRouter } from 'next/navigation';
import { createSupabaseBrowserClient } from '@/lib/supabase/client';

const supabase = createSupabaseBrowserClient();
// shadcn/ui components
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import Link from 'next/link';

// Zod schema for form validation
const loginSchema = z.object({
  email: z.string().email({ message: 'Please enter a valid email address.' }),
  password: z.string().min(6, { message: 'Password must be at least 6 characters.' }),
});

type LoginFormValues = z.infer<typeof loginSchema>;

export function LoginForm() {
  const router = useRouter();
  const [apiError, setApiError] = React.useState<string | null>(null);
  const [loading, setLoading] = React.useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<LoginFormValues>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: '',
      password: '',
    },
  });

  const onSubmit = async (data: LoginFormValues) => {
    setLoading(true);
    setApiError(null);

    const { email, password } = data;
    try {
      const { error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        setApiError(error.message || 'Invalid login credentials.');
        setLoading(false);
        return;
      }

      router.push('/dashboard');
    } catch (err) {
      if (
        typeof err === 'object' &&
        err !== null &&
        'message' in err &&
        typeof (err as { message?: unknown }).message === 'string'
      ) {
        setApiError((err as { message: string }).message);
      } else {
        setApiError('An unexpected error occurred. Please try again.');
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card className='max-w-md w-full mx-auto mt-8 shadow-lg'>
      <CardHeader>
        <CardTitle className='text-center text-2xl font-bold'>Sign in to NAVsync.io</CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit(onSubmit)} className='space-y-6' autoComplete='off' noValidate>
          <div className='space-y-2'>
            <Label htmlFor='email'>Email</Label>
            <Input
              id='email'
              type='email'
              autoComplete='email'
              placeholder='<EMAIL>'
              disabled={loading}
              {...register('email')}
              className={errors.email ? 'border-red-500' : ''}
            />
            {errors.email && <div className='text-red-600 text-sm'>{errors.email.message}</div>}
          </div>

          <div className='space-y-2'>
            <Label htmlFor='password'>Password</Label>
            <Input
              id='password'
              type='password'
              autoComplete='current-password'
              placeholder='Your password'
              disabled={loading}
              {...register('password')}
              className={errors.password ? 'border-red-500' : ''}
            />
            {errors.password && (
              <div className='text-red-600 text-sm'>{errors.password.message}</div>
            )}
          </div>

          <div className='text-right'>
            <Link href='/reset-password' className='text-sm underline'>
              Forgot password?
            </Link>
          </div>

          {apiError && (
            <div className='text-red-600 text-sm text-center' role='alert'>
              {apiError}
            </div>
          )}

          <Button type='submit' className='w-full' disabled={loading} aria-busy={loading}>
            {loading ? (
              <span>
                <span className='animate-spin inline-block mr-2'>&#9696;</span>
                Signing in...
              </span>
            ) : (
              'Sign In'
            )}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
}

export default LoginForm;
