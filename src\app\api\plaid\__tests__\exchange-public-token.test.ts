import { POST } from '../exchange-public-token/route';
import { plaidClient } from '../../../../lib/plaid';
import { createSupabaseServerClient } from '../../../../lib/supabase/server';
import { NextRequest } from 'next/server';

jest.mock('../../../../lib/plaid', () => ({
  plaidClient: {
    itemPublicTokenExchange: jest.fn(),
  },
}));

jest.mock('../../../../lib/supabase/server', () => ({
  createSupabaseServerClient: jest.fn(),
}));

interface ExchangeTokenRequest {
  public_token: string;
  metadata: {
    institution: {
      institution_id: string;
      name: string;
    };
  };
}

describe('POST /api/plaid/exchange-public-token', () => {
  const mockRequestBody: ExchangeTokenRequest = {
    public_token: 'public-sandbox-test-token',
    metadata: {
      institution: {
        institution_id: 'ins_test',
        name: 'Test Bank',
      },
    },
  };

  const mockPlaidResponse = {
    data: {
      access_token: 'access-sandbox-test-token',
      item_id: 'test-item-id',
    },
  };

  const mockUser = {
    id: 'test-user-id',
    email: '<EMAIL>',
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  const createMockRequest = (body: ExchangeTokenRequest | Partial<ExchangeTokenRequest>) => {
    return {
      json: jest.fn().mockResolvedValue(body),
    } as unknown as NextRequest;
  };

  test('Success (200 OK)', async () => {
    // Create mock insert function
    const mockInsert = jest.fn().mockResolvedValue({ error: null });
    const mockFrom = jest.fn().mockReturnValue({
      insert: mockInsert,
    });

    // Simulate an authenticated user
    (createSupabaseServerClient as jest.Mock).mockResolvedValue({
      auth: {
        getUser: jest.fn().mockResolvedValue({
          data: { user: mockUser },
          error: null,
        }),
      },
      from: mockFrom,
    });

    // Mock Plaid client to resolve successfully
    (plaidClient.itemPublicTokenExchange as jest.Mock).mockResolvedValue(mockPlaidResponse);

    const req = createMockRequest(mockRequestBody);
    const res = await POST(req);

    expect(res.status).toBe(200);
    const data = await res.json();
    expect(data.ok).toBe(true);

    // Verify Plaid client was called with correct parameters
    expect(plaidClient.itemPublicTokenExchange).toHaveBeenCalledWith({
      public_token: mockRequestBody.public_token,
    });

    // Verify database insert was called with correct parameters
    expect(mockFrom).toHaveBeenCalledWith('financial_accounts');
    expect(mockInsert).toHaveBeenCalledWith({
      user_id: mockUser.id,
      plaid_item_id: mockPlaidResponse.data.item_id,
      plaid_account_id: `temp_${mockPlaidResponse.data.item_id}`,
      account_name: `${mockRequestBody.metadata.institution.name} Account`,
      account_type: 'other',
      institution_name: mockRequestBody.metadata.institution.name,
      institution_id: mockRequestBody.metadata.institution.institution_id,
      plaid_metadata: {
        access_token: mockPlaidResponse.data.access_token,
        item_id: mockPlaidResponse.data.item_id,
        institution: mockRequestBody.metadata.institution,
        exchange_timestamp: expect.any(String),
      },
    });
  });

  test('Unauthorized (401)', async () => {
    // Mock auth.getUser() to return no user
    (createSupabaseServerClient as jest.Mock).mockResolvedValue({
      auth: {
        getUser: jest.fn().mockResolvedValue({
          data: { user: null },
          error: null,
        }),
      },
    });

    const req = createMockRequest(mockRequestBody);
    const res = await POST(req);

    expect(res.status).toBe(401);
    const data = await res.json();
    expect(data.error).toBe('Unauthorized');

    // Verify Plaid client was not called
    expect(plaidClient.itemPublicTokenExchange).not.toHaveBeenCalled();
  });

  test('Unauthorized (401) - Auth Error', async () => {
    // Mock auth.getUser() to return an error
    (createSupabaseServerClient as jest.Mock).mockResolvedValue({
      auth: {
        getUser: jest.fn().mockResolvedValue({
          data: { user: null },
          error: new Error('Auth error'),
        }),
      },
    });

    const req = createMockRequest(mockRequestBody);
    const res = await POST(req);

    expect(res.status).toBe(401);
    const data = await res.json();
    expect(data.error).toBe('Unauthorized');

    // Verify Plaid client was not called
    expect(plaidClient.itemPublicTokenExchange).not.toHaveBeenCalled();
  });

  test('Missing Required Fields (400)', async () => {
    const invalidRequestBody = {
      public_token: 'test-token',
      // Missing metadata.institution
    };

    const req = createMockRequest(invalidRequestBody);
    const res = await POST(req);

    expect(res.status).toBe(400);
    const data = await res.json();
    expect(data.error).toBe('Missing required fields: public_token and metadata.institution');

    // Verify no external services were called
    expect(createSupabaseServerClient).not.toHaveBeenCalled();
    expect(plaidClient.itemPublicTokenExchange).not.toHaveBeenCalled();
  });

  test('Missing Public Token (400)', async () => {
    const invalidRequestBody = {
      // Missing public_token
      metadata: {
        institution: {
          institution_id: 'ins_test',
          name: 'Test Bank',
        },
      },
    };

    const req = createMockRequest(invalidRequestBody);
    const res = await POST(req);

    expect(res.status).toBe(400);
    const data = await res.json();
    expect(data.error).toBe('Missing required fields: public_token and metadata.institution');

    // Verify no external services were called
    expect(createSupabaseServerClient).not.toHaveBeenCalled();
    expect(plaidClient.itemPublicTokenExchange).not.toHaveBeenCalled();
  });

  test('Plaid Client Error (500)', async () => {
    // Simulate an authenticated user
    (createSupabaseServerClient as jest.Mock).mockResolvedValue({
      auth: {
        getUser: jest.fn().mockResolvedValue({
          data: { user: mockUser },
          error: null,
        }),
      },
    });

    // Mock itemPublicTokenExchange to throw an error
    (plaidClient.itemPublicTokenExchange as jest.Mock).mockRejectedValue(
      new Error('Plaid API Error')
    );

    const req = createMockRequest(mockRequestBody);
    const res = await POST(req);

    expect(res.status).toBe(500);
    const data = await res.json();
    expect(data.error).toBe('Internal Server Error');

    // Verify Plaid client was called
    expect(plaidClient.itemPublicTokenExchange).toHaveBeenCalledWith({
      public_token: mockRequestBody.public_token,
    });
  });

  test('Database Error (500)', async () => {
    // Create mock insert function that returns an error
    const mockInsert = jest.fn().mockResolvedValue({
      error: new Error('Database insertion failed'),
    });
    const mockFrom = jest.fn().mockReturnValue({
      insert: mockInsert,
    });

    // Simulate an authenticated user
    (createSupabaseServerClient as jest.Mock).mockResolvedValue({
      auth: {
        getUser: jest.fn().mockResolvedValue({
          data: { user: mockUser },
          error: null,
        }),
      },
      from: mockFrom,
    });

    // Mock Plaid client to resolve successfully
    (plaidClient.itemPublicTokenExchange as jest.Mock).mockResolvedValue(mockPlaidResponse);

    const req = createMockRequest(mockRequestBody);
    const res = await POST(req);

    expect(res.status).toBe(500);
    const data = await res.json();
    expect(data.error).toBe('Failed to store account credentials');

    // Verify Plaid client was called successfully
    expect(plaidClient.itemPublicTokenExchange).toHaveBeenCalledWith({
      public_token: mockRequestBody.public_token,
    });

    // Verify database insert was attempted
    expect(mockFrom).toHaveBeenCalledWith('financial_accounts');
    expect(mockInsert).toHaveBeenCalled();
  });

  test('JSON Parsing Error (500)', async () => {
    // Create a request that will fail JSON parsing
    const req = {
      json: jest.fn().mockRejectedValue(new Error('Invalid JSON')),
    } as unknown as NextRequest;

    const res = await POST(req);

    expect(res.status).toBe(500);
    const data = await res.json();
    expect(data.error).toBe('Internal Server Error');

    // Verify no external services were called
    expect(createSupabaseServerClient).not.toHaveBeenCalled();
    expect(plaidClient.itemPublicTokenExchange).not.toHaveBeenCalled();
  });
});
