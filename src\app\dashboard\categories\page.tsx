'use client';

import { useAuth } from '@/components/auth/AuthProvider';
import CategoryManager from '@/components/categories/CategoryManager';
import Link from 'next/link';

export default function CategoriesPage() {
  const { user, isLoading } = useAuth();

  if (isLoading) {
    return (
      <div className='flex flex-col items-center justify-center min-h-screen p-4'>Loading...</div>
    );
  }

  if (!user) {
    return (
      <div className='flex flex-col items-center justify-center min-h-screen p-4'>
        <p>Please log in to access your categories.</p>
        <Link href='/profile' className='mt-4 text-blue-600 hover:underline'>
          Go to Profile
        </Link>
      </div>
    );
  }

  return (
    <div className='container mx-auto p-4'>
      <div className='mb-6'>
        <h1 className='text-3xl font-bold mb-2'>Categories</h1>
        <p className='text-gray-600'>Create and manage your custom spending categories</p>
      </div>
      
      <CategoryManager />
      
      <div className='mt-6'>
        <Link href='/dashboard' className='text-blue-600 hover:underline'>
          ← Back to Dashboard
        </Link>
      </div>
    </div>
  );
}