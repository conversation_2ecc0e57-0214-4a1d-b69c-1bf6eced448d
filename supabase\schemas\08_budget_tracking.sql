-- Budget vs actual tracking
CREATE TABLE budget_tracking (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    budget_id UUID NOT NULL REFERENCES budgets(id) ON DELETE CASCADE,
    category_id UUID NOT NULL REFERENCES transaction_categories(id) ON DELETE CASCADE,
    allocated_amount DECIMAL(15,2) NOT NULL DEFAULT 0,
    spent_amount DECIMAL(15,2) NOT NULL DEFAULT 0,
    remaining_amount DECIMAL(15,2) GENERATED ALWAYS AS (allocated_amount - spent_amount) STORED,
    percentage_used DECIMAL(5,2) GENERATED ALWAYS AS (
        CASE 
            WHEN allocated_amount > 0 THEN (spent_amount / allocated_amount * 100)
            ELSE 0 
        END
    ) STORED,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(budget_id, category_id)
);

-- Indexes
CREATE INDEX idx_budget_tracking_budget_id ON budget_tracking(budget_id);
CREATE INDEX idx_budget_tracking_category_id ON budget_tracking(category_id);

-- Enable RLS
ALTER TABLE budget_tracking ENABLE ROW LEVEL SECURITY;

-- R<PERSON> Policies
CREATE POLICY "Users can view own budget tracking" ON budget_tracking
    FOR SELECT
    TO authenticated
    USING (
        budget_id IN (
            SELECT id FROM budgets
            WHERE user_id = (SELECT auth.uid())
        )
    );

CREATE POLICY "Users can insert own budget tracking" ON budget_tracking
    FOR INSERT
    TO authenticated
    WITH CHECK (
        budget_id IN (
            SELECT id FROM budgets
            WHERE user_id = (SELECT auth.uid())
        )
    );

CREATE POLICY "Users can update own budget tracking" ON budget_tracking
    FOR UPDATE
    TO authenticated
    USING (
        budget_id IN (
            SELECT id FROM budgets
            WHERE user_id = (SELECT auth.uid())
        )
    )
    WITH CHECK (
        budget_id IN (
            SELECT id FROM budgets
            WHERE user_id = (SELECT auth.uid())
        )
    );

CREATE POLICY "Users can delete own budget tracking" ON budget_tracking
    FOR DELETE
    TO authenticated
    USING (
        budget_id IN (
            SELECT id FROM budgets
            WHERE user_id = (SELECT auth.uid())
        )
    );

-- Trigger for updated_at
CREATE TRIGGER update_budget_tracking_updated_at BEFORE UPDATE ON budget_tracking
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Comments
COMMENT ON TABLE budget_tracking IS 'Budget vs actual spending tracking with computed fields';
COMMENT ON COLUMN budget_tracking.remaining_amount IS 'Computed field: allocated_amount - spent_amount';
COMMENT ON COLUMN budget_tracking.percentage_used IS 'Computed field: (spent_amount / allocated_amount) * 100';
 