'use client';

import { useAuth } from '@/components/auth/AuthProvider';
import PlaidLink from '@/components/plaid/PlaidLink';
import Link from 'next/link';
import AccountsList from '@/components/plaid/AccountsList';

export default function DashboardPage() {
  const { user, isLoading } = useAuth();

  if (isLoading) {
    return (
      <div className='flex flex-col items-center justify-center min-h-screen p-4'>Loading...</div>
    );
  }

  if (!user) {
    return (
      <div className='flex flex-col items-center justify-center min-h-screen p-4'>
        <p>Please log in to access the dashboard.</p>
        <Link href='/profile' className='mt-4 text-blue-600 hover:underline'>
          Go to Profile
        </Link>
      </div>
    );
  }

  return (
    <div className='flex flex-col items-center justify-center min-h-screen p-4'>
      <h1 className='text-3xl font-bold mb-4'>Dashboard</h1>
      <PlaidLink userId={user.id} />
      <div className='mt-6 w-full flex justify-center'>
        <AccountsList />
      </div>
      <Link href='/profile' className='mt-4 text-blue-600 hover:underline'>
        Go to Profile
      </Link>
    </div>
  );
}
