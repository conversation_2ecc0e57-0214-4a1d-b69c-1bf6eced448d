import { GET } from '../get/route';
import { createSupabaseServerClient } from '@/lib/supabase/server';
import { SupabaseClient } from '@supabase/supabase-js';

jest.mock('@/lib/supabase/server');
jest.mock('next/headers', () => ({
  cookies: jest.fn().mockReturnValue({
    getAll: jest.fn(),
    set: jest.fn(),
  }),
}));

const mockCreateSupabaseServerClient = createSupabaseServerClient as jest.Mock;

describe('GET /api/categories', () => {
  const mockUser = { id: 'user-123' };
  const mockCategories = [
    {
      id: 'cat-1',
      name: 'Category 1',
      description: 'Description 1',
      base_category_id: null,
      icon: 'icon1',
      color: 'red',
      is_active: true,
      created_at: '2025-01-01T00:00:00Z',
      updated_at: '2025-01-02T00:00:00Z',
    },
    {
      id: 'cat-2',
      name: 'Category 2',
      description: 'Description 2',
      base_category_id: 'base-1',
      icon: 'icon2',
      color: 'blue',
      is_active: true,
      created_at: '2025-01-03T00:00:00Z',
      updated_at: '2025-01-04T00:00:00Z',
    },
  ];

  let mockSupabaseClient: Partial<SupabaseClient>;
  let mockQueryBuilder: {
    select: jest.Mock;
    eq: jest.Mock;
    order: jest.Mock;
  };

  beforeEach(() => {
    mockQueryBuilder = {
      select: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      order: jest.fn().mockResolvedValue({ data: mockCategories, error: null }),
    };

    const mockAuth = {
      getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
    };

    mockSupabaseClient = {
      auth: mockAuth as unknown as SupabaseClient['auth'],
      from: jest.fn().mockReturnValue(mockQueryBuilder),
    };

    mockCreateSupabaseServerClient.mockResolvedValue(
      mockSupabaseClient as unknown as SupabaseClient
    );
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  test('Success (200 OK) returns categories for authenticated user', async () => {
    const response = await GET();
    const json = await response.json();

    expect(response.status).toBe(200);
    expect(json.categories).toEqual(mockCategories);
    expect(mockSupabaseClient.auth!.getUser).toHaveBeenCalledTimes(1);
    expect(mockSupabaseClient.from).toHaveBeenCalledWith('user_categories');
    expect(mockQueryBuilder.eq).toHaveBeenCalledWith('user_id', mockUser.id);
    expect(mockQueryBuilder.eq).toHaveBeenCalledWith('is_active', true);
    expect(mockQueryBuilder.order).toHaveBeenCalledWith('name', { ascending: true });
  });

  test('Unauthorized (401) returns error when no user', async () => {
    (mockSupabaseClient.auth!.getUser as jest.Mock).mockResolvedValueOnce({
      data: { user: null },
      error: null,
    });

    const response = await GET();
    const json = await response.json();

    expect(response.status).toBe(401);
    expect(json.error).toBe('Unauthorized');
  });

  test('Database error (500) returns error when select returns error', async () => {
    const dbError = { message: 'DB error', code: '500' };
    mockQueryBuilder.order.mockResolvedValueOnce({ data: null, error: dbError });

    const response = await GET();
    const json = await response.json();

    expect(response.status).toBe(500);
    expect(json.error).toBe('Failed to fetch categories');
  });
});