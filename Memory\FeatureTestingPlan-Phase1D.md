# NAVsync: Feature Testing Plan - Phase 1D (Plaid, Transactions, Categories)

## 1. Introduction

This document outlines the testing plan for the features recently completed: Plaid integration, transaction management, and category management. This plan builds upon the foundation established in the `AutomatedTestingPlan.md` and focuses on creating comprehensive test coverage for all new backend API routes and frontend components.

The primary goal is to ensure the reliability, security, and correctness of these core features before proceeding to the development of the Budgeting system.

## 2. Overall Strategy

We will continue to follow the "Testing Pyramid" strategy, utilizing a mix of unit and integration tests.

-   **Backend (API Routes):** Tests will be treated as integration tests. We will test the Next.js API routes by mocking external dependencies (Plaid, Supabase) to verify our business logic, request handling, and error states. We will use Jest for this.
-   **Frontend (React Components):** We will use a combination of unit and integration tests with Jest and React Testing Library (RTL). Components will be tested in isolation, and we will mock `fetch` requests to our backend API to simulate different states (loading, success, error).

```mermaid
graph TD
    subgraph "Backend API Testing (Jest)"
        A["API Route Tests (e.g., /api/transactions/get)"]
        B["Mock Supabase Client"]
        C["Mock Plaid Client"]
        A --> B
        A --> C
    end

    subgraph "Frontend Component Testing (Jest + RTL)"
        D["Component Tests (e.g., TransactionsList)"]
        E["Mock 'fetch' API"]
        F["Simulate User Interaction"]
        D --> E
        D --> F
    end
```

## 3. Backend API Route Testing (`src/app/api/`)

### 3.1. Mocking Strategy

To test the API routes in isolation, we must mock all external services.

-   **Mocking Supabase Client (`@supabase/ssr`):**
    We will use Jest's `jest.mock()` functionality to mock the `createClient` function from [`src/lib/supabase/server.ts`](src/lib/supabase/server.ts). This will allow us to provide mock implementations for `auth.getUser()`, `from().select()`, `from().insert()`, etc., for each test case.

-   **Mocking Plaid Client (`plaid`):**
    Similarly, we will mock the `plaid` client from [`src/lib/plaid.ts`](src/lib/plaid.ts). This allows us to control the responses from Plaid's API for creating link tokens, exchanging public tokens, and syncing transactions.

### 3.2. API Test Cases

For each endpoint group, we will create a separate test file (e.g., `plaid.test.ts`, `transactions.test.ts`). Each file will test for the following scenarios:

-   **Success (200 OK):** The request is valid, the user is authenticated, and the operation succeeds.
-   **Authentication Failure (401 Unauthorized):** The user is not authenticated (mock `auth.getUser()` returns no user).
-   **Server Error (500 Internal Server Error):** An unexpected error occurs (e.g., the mocked Plaid/Supabase client throws an error).

---

## 4. Frontend Component Testing

### 4.1. Mocking Strategy

-   **Mocking `fetch` API:**
    We will use `jest.fn()` to mock the global `fetch` function. This allows us to simulate API responses from our own backend (`/api/...`) and test how our components handle different data, loading, and error states. We can use `mockResolvedValueOnce` and `mockRejectedValueOnce` to simulate these scenarios.

### 4.2. Component Test Cases

For each component, we will test the following:

-   **Initial Rendering:** The component renders correctly with different sets of props.
-   **Loading States:** The component displays a loading indicator (e.g., `LoadingSpinner`) while data is being fetched.
-   **Error States:** The component displays an appropriate error message when an API call fails.
-   **User Interaction:** User actions (e.g., button clicks, form submissions) trigger the correct functions and API calls with the expected parameters.
-   **Data Display:** The component correctly renders data returned from the API.

---

## 5. Task Breakdown & Delegation

The following table breaks down the testing tasks and suggests an appropriate developer level for delegation.

| Task ID      | Feature Area         | Description                                                 | File(s) to Test                                                                                                                              | Recommended Mode |
| :----------- | :------------------- | :---------------------------------------------------------- | :------------------------------------------------------------------------------------------------------------------------------------------- | :--------------- |
| **API-PLD-01** | API: Plaid           | Test `create-link-token` route (Success, 401, 500)          | [`src/app/api/plaid/create-link-token/route.ts`](src/app/api/plaid/create-link-token/route.ts)                                                 | Midlevel         |
| **API-PLD-02** | API: Plaid           | Test `exchange-public-token` route (Success, 401, 500)      | [`src/app/api/plaid/exchange-public-token/route.ts`](src/app/api/plaid/exchange-public-token/route.ts)                                         | Senior           |
| **API-PLD-03** | API: Plaid           | Test `get-accounts` route (Success, 401, 500)               | [`src/app/api/plaid/get-accounts/route.ts`](src/app/api/plaid/get-accounts/route.ts)                                                           | Midlevel         |
| **API-PLD-04** | API: Plaid           | Test `sync-transactions` route (Success, 401, 500)          | [`src/app/api/plaid/sync-transactions/route.ts`](src/app/api/plaid/sync-transactions/route.ts)                                                 | Senior           |
| **API-TRN-01** | API: Transactions    | Test `get` transactions route (Success, 401, 500)           | [`src/app/api/transactions/get/route.ts`](src/app/api/transactions/get/route.ts)                                                               | Midlevel         |
| **API-TRN-02** | API: Transactions    | Test `categorize` transaction route (Success, 401, 500)     | [`src/app/api/transactions/categorize/route.ts`](src/app/api/transactions/categorize/route.ts)                                                 | Midlevel         |
| **API-CAT-01** | API: Categories      | Test `get` categories route (Success, 401, 500)             | [`src/app/api/categories/get/route.ts`](src/app/api/categories/get/route.ts)                                                                   | Junior           |
| **API-CAT-02** | API: Categories      | Test `create` category route (Success, 401, 500)            | [`src/app/api/categories/create/route.ts`](src/app/api/categories/create/route.ts)                                                             | Midlevel         |
| **API-CAT-03** | API: Categories      | Test `update` category route (Success, 401, 500)            | [`src/app/api/categories/update/route.ts`](src/app/api/categories/update/route.ts)                                                             | Midlevel         |
| **API-CAT-04** | API: Categories      | Test `delete` category route (Success, 401, 500)            | [`src/app/api/categories/delete/route.ts`](src/app/api/categories/delete/route.ts)                                                             | Midlevel         |
| **FE-PLD-01**  | Frontend: Plaid      | Test `PlaidLink` component (Rendering, user interaction)    | [`src/components/plaid/PlaidLink.tsx`](src/components/plaid/PlaidLink.tsx)                                                                   | Midlevel         |
| **FE-PLD-02**  | Frontend: Plaid      | Test `AccountsList` & `AccountCard` (Rendering, props)      | [`src/components/plaid/AccountsList.tsx`](src/components/plaid/AccountsList.tsx), [`src/components/plaid/AccountCard.tsx`](src/components/plaid/AccountCard.tsx) | Junior           |
| **FE-TRN-01**  | Frontend: Transactions | Test `TransactionsList` (Loading, error, data display)      | [`src/components/transactions/TransactionsList.tsx`](src/components/transactions/TransactionsList.tsx)                                       | Midlevel         |
| **FE-TRN-02**  | Frontend: Transactions | Test `TransactionCard` (Rendering, props, interaction)      | [`src/components/transactions/TransactionCard.tsx`](src/components/transactions/TransactionCard.tsx)                                         | Junior           |
| **FE-CAT-01**  | Frontend: Categories | Test `CategoryManager` (Full CRUD interaction, loading, error) | [`src/components/categories/CategoryManager.tsx`](src/components/categories/CategoryManager.tsx)                                             | Senior           |
| **FE-CAT-02**  | Frontend: Categories | Test `CategorySelector` (Rendering, selection change)       | [`src/components/categories/CategorySelector.tsx`](src/components/categories/CategorySelector.tsx)                                           | Junior           |