import type { NextConfig } from 'next';
import { env } from './src/env.mjs'; // Added import

// Optional: Log to confirm env is loaded and validated.
// This also makes `env` "used" to satisfy linters if no other use is present.
if (env) {
  console.log('✅ Environment variables loaded and validated for next.config.ts');
}

const nextConfig: NextConfig = {
  /* config options here */
  // You could potentially use env variables here if needed for the Next.js config, e.g.
  // env: {
  //   MY_VARIABLE: env.MY_VARIABLE,
  // },
};

export default nextConfig;
