import { POST } from '../sync-transactions/route';
import { createSupabaseServerClient } from '@/lib/supabase/server';
import { plaidClient } from '@/lib/plaid';
import { NextRequest } from 'next/server';
import { AxiosHeaders, AxiosResponse } from 'axios';
import {
  RemovedTransaction,
  Transaction,
  TransactionsSyncResponse,
  TransactionsUpdateStatus,
} from 'plaid';

// Mock dependencies
jest.mock('@/lib/supabase/server', () => ({
  createSupabaseServerClient: jest.fn(),
}));

jest.mock('@/lib/plaid', () => ({
  plaidClient: {
    transactionsSync: jest.fn(),
  },
}));

// Mock next/server
jest.mock('next/server', () => ({
  ...jest.requireActual('next/server'),
  NextResponse: {
    json: jest.fn((body, init) => ({
      status: init?.status || 200,
      json: () => Promise.resolve(body),
    })),
  },
}));

describe('POST /api/plaid/sync-transactions', () => {
  const mockUser = { id: 'test-user-id' };
  const mockItemId = 'test-item-id';
  const mockAccessToken = 'test-access-token';
  const mockCursor = 'test-cursor';
  const mockAccountId = 'test-account-id';

  interface MockRequestBody {
    item_id: string;
  }

  const mockFinancialAccount = {
    id: mockAccountId,
    plaid_metadata: {
      access_token: mockAccessToken,
      cursor: mockCursor,
    },
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  const createMockRequest = (body: MockRequestBody) => {
    return new Request('http://localhost/api/plaid/sync-transactions', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(body),
    }) as NextRequest;
  };

  test('Success (200 OK) with new, updated, and removed transactions', async () => {
    const mockUpsert = jest.fn().mockResolvedValue({ error: null });
    const mockUpdate = jest.fn().mockResolvedValue({ error: null });

    const mockSupabase = {
      auth: {
        getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
      },
      from: jest.fn().mockImplementation((table: string) => {
        if (table === 'financial_accounts') {
          return {
            select: jest.fn().mockReturnThis(),
            eq: jest.fn().mockReturnThis(),
            single: jest.fn().mockResolvedValue({ data: mockFinancialAccount, error: null }),
            update: jest.fn().mockReturnThis(),
          };
        }
        if (table === 'transactions') {
          return {
            upsert: mockUpsert,
            update: jest.fn().mockReturnThis(),
            eq: jest.fn().mockImplementation(() => ({
              eq: mockUpdate,
            })),
          };
        }
        return {
          update: jest.fn().mockReturnThis(),
          eq: jest.fn().mockResolvedValue({ error: null }),
        };
      }),
    };
    (createSupabaseServerClient as jest.Mock).mockResolvedValue(mockSupabase);

    const mockPlaidResponseData: TransactionsSyncResponse = {
      added: [
        {
          transaction_id: 'added-1',
          amount: 10,
          name: 'New Purchase',
          category: ['Food and Drink', 'Restaurants'],
          date: '2025-06-08',
        } as Transaction,
      ],
      modified: [
        {
          transaction_id: 'modified-1',
          amount: 20,
          name: 'Updated Purchase',
          category: ['Travel', 'Airlines'],
          date: '2025-06-08',
        } as Transaction,
      ],
      removed: [{ transaction_id: 'removed-1' } as RemovedTransaction],
      next_cursor: 'new-cursor',
      has_more: false,
      request_id: 'req-id',
      accounts: [],
      transactions_update_status: TransactionsUpdateStatus.InitialUpdateComplete,
    };

    const mockAxiosResponse: AxiosResponse<TransactionsSyncResponse> = {
      data: mockPlaidResponseData,
      status: 200,
      statusText: 'OK',
      headers: {},
      config: { headers: new AxiosHeaders() },
    };

    (plaidClient.transactionsSync as jest.Mock).mockResolvedValue(mockAxiosResponse);

    const req = createMockRequest({ item_id: mockItemId });
    const res = await POST(req);

    expect(res.status).toBe(200);
    const body = await res.json();
    expect(body.summary).toEqual({
      added: 1,
      modified: 1,
      removed: 1,
      next_cursor: 'new-cursor',
    });

    expect(mockUpsert).toHaveBeenCalledTimes(2);
    expect(mockUpdate).toHaveBeenCalledTimes(1);
  });

  test('Unauthorized (401)', async () => {
    (createSupabaseServerClient as jest.Mock).mockResolvedValue({
      auth: {
        getUser: jest
          .fn()
          .mockResolvedValue({ data: { user: null }, error: { message: 'Unauthorized' } }),
      },
    });

    const req = createMockRequest({ item_id: mockItemId });
    const res = await POST(req);

    expect(res.status).toBe(401);
    expect(await res.json()).toEqual({ error: 'Unauthorized' });
  });

  test('Account Not Found (404)', async () => {
    (createSupabaseServerClient as jest.Mock).mockResolvedValue({
      auth: {
        getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
      },
      from: jest.fn().mockReturnValue({
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({ data: null, error: { message: 'Not Found' } }),
      }),
    });

    const req = createMockRequest({ item_id: mockItemId });
    const res = await POST(req);

    expect(res.status).toBe(404);
    expect(await res.json()).toEqual({ error: 'Financial account not found for this item_id' });
  });

  test('Plaid Client Error (500)', async () => {
    (createSupabaseServerClient as jest.Mock).mockResolvedValue({
      auth: {
        getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
      },
      from: jest.fn().mockReturnValue({
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({ data: mockFinancialAccount, error: null }),
      }),
    });

    (plaidClient.transactionsSync as jest.Mock).mockRejectedValue(new Error('Plaid API error'));

    const req = createMockRequest({ item_id: mockItemId });
    const res = await POST(req);

    expect(res.status).toBe(500);
    expect(await res.json()).toEqual({ error: 'Internal Server Error' });
  });

  test('Database Error during Sync (500)', async () => {
    const dbError = { message: 'DB upsert failed' };
    (createSupabaseServerClient as jest.Mock).mockResolvedValue({
      auth: {
        getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
      },
      from: jest.fn().mockImplementation((table: string) => {
        if (table === 'financial_accounts') {
          return {
            select: jest.fn().mockReturnThis(),
            eq: jest.fn().mockReturnThis(),
            single: jest.fn().mockResolvedValue({ data: mockFinancialAccount, error: null }),
          };
        }
        if (table === 'transactions') {
          return {
            upsert: jest.fn().mockResolvedValue({ error: dbError }),
          };
        }
      }),
    });

    const mockPlaidResponseData: TransactionsSyncResponse = {
      added: [
        {
          transaction_id: 'added-1',
          amount: 10,
          name: 'New Purchase',
          date: '2025-06-08',
        } as Transaction,
      ],
      modified: [],
      removed: [],
      next_cursor: 'new-cursor',
      has_more: false,
      request_id: 'req-id',
      accounts: [],
      transactions_update_status: TransactionsUpdateStatus.InitialUpdateComplete,
    };
    const mockAxiosResponse: AxiosResponse<TransactionsSyncResponse> = {
      data: mockPlaidResponseData,
      status: 200,
      statusText: 'OK',
      headers: {},
      config: { headers: new AxiosHeaders() },
    };
    (plaidClient.transactionsSync as jest.Mock).mockResolvedValue(mockAxiosResponse);

    const req = createMockRequest({ item_id: mockItemId });
    const res = await POST(req);

    expect(res.status).toBe(500);
    expect(await res.json()).toEqual({ error: `Failed to upsert transaction: ${dbError.message}` });
  });
});
