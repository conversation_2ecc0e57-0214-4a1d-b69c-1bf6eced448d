'use client';

import React, { useEffect, useState } from 'react';

interface Category {
  id: string;
  name: string;
}

interface CategorySelectorProps {
  transaction_id: string;
  user_category_id: string | null;
}

/**
 * CategorySelector component provides a dropdown to select and assign a category to a transaction.
 * It fetches available categories and updates the transaction's category via API on selection change.
 *
 * @component
 * @param {Object} props - Component props
 * @param {string} props.transaction_id - The ID of the transaction to categorize
 * @param {string | null} props.user_category_id - The currently assigned user category ID, if any
 * @returns {JSX.Element} The rendered category selector dropdown
 */
export default function CategorySelector({ transaction_id, user_category_id }: CategorySelectorProps) {
  const [categories, setCategories] = useState<Category[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string | null>(user_category_id);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [updating, setUpdating] = useState(false);

  useEffect(() => {
    async function fetchCategories() {
      setLoading(true);
      setError(null);
      try {
        const res = await fetch('/api/categories/get');
        if (!res.ok) {
          throw new Error('Failed to fetch categories');
        }
        const data = await res.json();
        setCategories(data.categories || []);
      } catch (err) {
        setError((err as Error).message);
      } finally {
        setLoading(false);
      }
    }
    fetchCategories();
  }, []);

  useEffect(() => {
    setSelectedCategory(user_category_id);
  }, [user_category_id]);

  async function handleChange(event: React.ChangeEvent<HTMLSelectElement>) {
    const newCategoryId = event.target.value || null;
    setSelectedCategory(newCategoryId);
    setUpdating(true);
    setError(null);

    // Validate that the selected category exists in the available categories list
    if (newCategoryId && !categories.find(cat => cat.id === newCategoryId)) {
      setError('Invalid category selected');
      setUpdating(false);
      return;
    }

    try {
      const res = await fetch('/api/transactions/categorize', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          transaction_id,
          user_category_id: newCategoryId
        })
      });
      if (!res.ok) {
        throw new Error('Failed to update category');
      }
    } catch (err) {
      setError((err as Error).message);
    } finally {
      setUpdating(false);
    }
  }

  if (loading) {
    return <div>Loading categories...</div>;
  }

  if (error) {
    return <div className="text-red-600 text-sm">Error: {error}</div>;
  }

  return (
    <div className="mt-3">
      <label htmlFor={`category-select-${transaction_id}`} className="block text-xs font-medium text-gray-700 mb-1">
        Category
      </label>
      <select
        id={`category-select-${transaction_id}`}
        value={selectedCategory || ''}
        onChange={handleChange}
        disabled={updating}
        className="block w-full rounded-md border border-gray-300 bg-white py-1 px-2 text-sm focus:border-indigo-500 focus:ring-indigo-500"
      >
        <option value="">Uncategorized</option>
        {categories.map((cat) => (
          <option key={cat.id} value={cat.id}>
            {cat.name}
          </option>
        ))}
      </select>
      {updating && <div className="text-xs text-gray-500 mt-1">Updating...</div>}
    </div>
  );
}