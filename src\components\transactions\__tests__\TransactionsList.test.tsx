import React from 'react';
import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import TransactionsList from '@/components/transactions/TransactionsList';

// Define interfaces for mocks
interface Transaction {
  id: string;
  amount: number;
  currency_code: string;
  transaction_date: string;
  authorized_date: string | null;
  posted_date: string | null;
  merchant_name: string | null;
  description: string;
  category_id: string | null;
  user_category_id: string | null;
  plaid_category: string[] | null;
  plaid_category_detailed: string[] | null;
  transaction_type: string;
  location: Record<string, unknown> | null;
  is_pending: boolean;
  is_recurring: boolean;
  status: string;
  tags: string[] | null;
  notes: string | null;
  created_at: string;
  updated_at: string;
  financial_accounts: {
    id: string;
    account_name: string;
    institution_name: string;
    account_type: string;
    account_subtype: string;
    mask: string | null;
  };
}

interface PaginationInfo {
  currentPage: number;
  pageSize: number;
  totalCount: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

// Mock TransactionCard to simplify testing
jest.mock('@/components/transactions/TransactionCard', () => {
  return {
    __esModule: true,
    default: ({ transaction }: { transaction: { id: string } }) => (
      <div data-testid={`transaction-card-${transaction.id}`}>Transaction Card</div>
    ),
  };
});

// Mock LoadingSpinner
jest.mock('@/components/ui/LoadingSpinner', () => {
  return {
    __esModule: true,
    default: () => <div data-testid='loading-spinner'>Loading...</div>,
  };
});

global.fetch = jest.fn();

const mockTransaction = (id: string): Transaction => ({
  id,
  amount: -100,
  currency_code: 'USD',
  transaction_date: '2025-06-08T12:00:00.000Z',
  authorized_date: null,
  posted_date: null,
  merchant_name: `Merchant ${id}`,
  description: `Purchase at Merchant ${id}`,
  category_id: null,
  user_category_id: null,
  plaid_category: ['Shopping'],
  plaid_category_detailed: ['Shopping', 'Retail'],
  transaction_type: 'debit',
  location: null,
  is_pending: false,
  is_recurring: false,
  status: 'posted',
  tags: null,
  notes: null,
  created_at: '2025-06-08T12:00:00.000Z',
  updated_at: '2025-06-08T12:00:00.000Z',
  financial_accounts: {
    id: 'acc_1',
    account_name: 'Checking',
    institution_name: 'Bank of Tests',
    account_type: 'depository',
    account_subtype: 'checking',
    mask: '1111',
  },
});

const mockApiResponse = (transactions: Transaction[], pagination: PaginationInfo) => ({
  ok: true,
  json: async () => ({
    transactions,
    pagination,
  }),
});

const mockApiError = (message: string) => ({
  ok: false,
  json: async () => ({ error: message }),
});

describe('TransactionsList', () => {
  beforeEach(() => {
    (fetch as jest.Mock).mockClear();
  });

  test('Test Case 1: Successful Data Fetch and Rendering', async () => {
    const transactions = [mockTransaction('1'), mockTransaction('2')];
    const pagination: PaginationInfo = {
      currentPage: 1,
      pageSize: 2,
      totalCount: 5,
      totalPages: 3,
      hasNextPage: true,
      hasPreviousPage: false,
    };
    (fetch as jest.Mock).mockResolvedValue(mockApiResponse(transactions, pagination));

    render(<TransactionsList />);

    await waitFor(() => {
      expect(screen.getAllByText('Transaction Card')).toHaveLength(2);
    });

    expect(screen.getByText('Showing 1 to 2 of 5 transactions')).toBeInTheDocument();
    expect(screen.getByText('Page 1 of 3')).toBeInTheDocument();
  });

  test('Test Case 2: Loading State', async () => {
    (fetch as jest.Mock).mockImplementation(() => new Promise(() => {})); // Never resolves

    render(<TransactionsList />);

    expect(screen.getByTestId('loading-spinner')).toBeInTheDocument();
    expect(screen.getByText('Loading transactions...')).toBeInTheDocument();
  });

  test('Test Case 3: Error State', async () => {
    const errorMessage = 'Network error';
    (fetch as jest.Mock).mockResolvedValue(mockApiError(errorMessage));

    render(<TransactionsList />);

    await waitFor(() => {
      expect(screen.getByText('Error Loading Transactions')).toBeInTheDocument();
    });
    expect(screen.getByText(errorMessage)).toBeInTheDocument();
  });

  test('Test Case 4: Empty State', async () => {
    const pagination: PaginationInfo = {
      currentPage: 1,
      pageSize: 10,
      totalCount: 0,
      totalPages: 0,
      hasNextPage: false,
      hasPreviousPage: false,
    };
    (fetch as jest.Mock).mockResolvedValue(mockApiResponse([], pagination));

    render(<TransactionsList />);

    await waitFor(() => {
      expect(screen.getByText('No Transactions Found')).toBeInTheDocument();
    });
  });

  test('Test Case 5: Pagination Interaction', async () => {
    const page1Transactions = [mockTransaction('1')];
    const page1Pagination: PaginationInfo = {
      currentPage: 1,
      pageSize: 1,
      totalCount: 2,
      totalPages: 2,
      hasNextPage: true,
      hasPreviousPage: false,
    };
    (fetch as jest.Mock).mockResolvedValueOnce(mockApiResponse(page1Transactions, page1Pagination));

    render(<TransactionsList />);

    await waitFor(() => {
      expect(screen.getByText('Showing 1 to 1 of 2 transactions')).toBeInTheDocument();
    });

    const nextButton = screen.getByRole('button', { name: /next/i });
    expect(nextButton).not.toBeDisabled();

    const page2Transactions = [mockTransaction('2')];
    const page2Pagination: PaginationInfo = {
      currentPage: 2,
      pageSize: 1,
      totalCount: 2,
      totalPages: 2,
      hasNextPage: false,
      hasPreviousPage: true,
    };
    (fetch as jest.Mock).mockResolvedValueOnce(mockApiResponse(page2Transactions, page2Pagination));

    fireEvent.click(nextButton);

    await waitFor(() => {
      expect(fetch).toHaveBeenCalledTimes(2);
    });

    expect(fetch).toHaveBeenCalledWith('/api/transactions/get?page=2&pageSize=20');

    await waitFor(() => {
      expect(screen.getByText('Showing 2 to 2 of 2 transactions')).toBeInTheDocument();
    });
    expect(screen.getByTestId('transaction-card-2')).toBeInTheDocument();
  });
});
