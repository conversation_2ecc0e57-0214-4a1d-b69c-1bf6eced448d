// src/lib/supabase/server.ts
import { createServerClient as _createServerClientInternal } from '@supabase/ssr';
import { cookies } from 'next/headers';

export async function createSupabaseServerClient() {
  const cookieStore = await cookies();
  return _createServerClientInternal(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return cookieStore.getAll();
        },
        setAll(cookiesToSet) {
          try {
            cookiesToSet.forEach(({ name, value, options }) =>
              cookieStore.set(name, value, options)
            );
          } catch {
            // The `setAll` method was called from a Server Component.
            // This can be ignored if you have middleware refreshing
            // user sessions.
          }
        },
      },
    }
  );
}

export function createServerClient(
  supabaseUrl: string,
  supabaseAnonKey: string,
  options: {
    cookies: {
      getAll(): { name: string; value: string }[];
      setAll(
        cookiesToSet: { name: string; value: string; options?: Record<string, unknown> }[]
      ): void;
    };
  }
) {
  return _createServerClientInternal(supabaseUrl, supabaseAnonKey, options);
}
