import { dirname } from "path";
import { fileURLToPath } from "url";
import { FlatCompat } from "@eslint/eslintrc";
import eslintConfigPrettier from "eslint-config-prettier"; // New import

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const compat = new FlatCompat({
  baseDirectory: __dirname,
});

const eslintConfig = [
  ...compat.extends("next/core-web-vitals", "next/typescript"),
  eslintConfigPrettier, // Added here
  {
    // Override rules for test files
    files: ["**/__tests__/**/*", "**/*.test.*", "**/*.spec.*"],
    rules: {
      "@typescript-eslint/no-explicit-any": "off", // Allow 'any' in test files for mocks
      "react/display-name": "off", // Allow missing display names in test mocks
      "@typescript-eslint/no-unused-vars": "off", // Allow unused vars in test files
    },
  },
];

export default eslintConfig;
