// Learn more: https://github.com/testing-library/jest-dom
import '@testing-library/jest-dom';

// Polyfill for Next.js API routes in test environment
import { TextEncoder, TextDecoder } from 'util';

global.TextEncoder = TextEncoder;
global.TextDecoder = TextDecoder;

// Mock Request and Response for Next.js API routes
if (typeof global.Request === 'undefined') {
  global.Request = class MockRequest {
    constructor(url, init) {
      this.url = url;
      this.init = init;
      this.body = init?.body;
    }
    json() {
      return Promise.resolve(JSON.parse(this.body || '{}'));
    }
  };
}

if (typeof global.Response === 'undefined') {
  global.Response = class MockResponse {
    constructor(body, init) {
      this.body = body;
      this.init = init;
      this.status = init?.status || 200;
    }
    json() {
      return Promise.resolve(JSON.parse(this.body || '{}'));
    }
    static json(data, init) {
      return new MockResponse(JSON.stringify(data), init);
    }
  };
}

// Mock Headers
if (typeof global.Headers === 'undefined') {
  global.Headers = class MockHeaders {
    constructor() {}
    get() {
      return null;
    }
    set() {}
  };
}

// Mock NextResponse
jest.mock('next/server', () => ({
  NextResponse: {
    json: (data, init) => ({
      json: () => Promise.resolve(data),
      status: init?.status || 200,
    }),
  },
}));
