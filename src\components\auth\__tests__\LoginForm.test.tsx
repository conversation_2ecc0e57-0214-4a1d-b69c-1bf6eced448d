import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { useRouter } from 'next/navigation';
import LoginForm from '../LoginForm';

// Mock next/navigation
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
}));

// Mock the Supabase client module
jest.mock('@/lib/supabase/client', () => {
  const mockAuth = {
    signInWithPassword: jest.fn(),
  };

  return {
    createSupabaseBrowserClient: jest.fn(() => ({
      auth: mockAuth,
    })),
    // Export the mock for test access
    __mockAuth: mockAuth,
  };
});

// Get access to the mock auth object
const { __mockAuth: mockAuth } = jest.requireMock('@/lib/supabase/client');

const mockPush = jest.fn();
const mockedUseRouter = useRouter as jest.Mock;

describe('LoginForm Integration Tests', () => {
  beforeEach(() => {
    // Reset all mocks before each test
    jest.clearAllMocks();

    // Setup router mock
    mockedUseRouter.mockReturnValue({
      push: mockPush,
    });
  });

  describe('Successful Login', () => {
    it('should redirect to dashboard on successful login', async () => {
      const user = userEvent.setup();

      // Mock successful authentication
      mockAuth.signInWithPassword.mockResolvedValue({
        error: null,
        data: { user: { id: '123', email: '<EMAIL>' } },
      });

      render(<LoginForm />);

      // Fill out the form
      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const submitButton = screen.getByRole('button', { name: /sign in/i });

      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'password123');

      // Submit the form
      await user.click(submitButton);

      // Wait for the async operation to complete
      await waitFor(() => {
        expect(mockAuth.signInWithPassword).toHaveBeenCalledWith({
          email: '<EMAIL>',
          password: 'password123',
        });
      });

      // Assert that router.push was called with the correct path
      expect(mockPush).toHaveBeenCalledWith('/dashboard');
    });

    it('should show loading state during authentication', async () => {
      const user = userEvent.setup();

      // Mock a delayed response
      mockAuth.signInWithPassword.mockImplementation(
        () => new Promise((resolve) => setTimeout(() => resolve({ error: null }), 100))
      );

      render(<LoginForm />);

      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const submitButton = screen.getByRole('button', { name: /sign in/i });

      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'password123');
      await user.click(submitButton);

      // Check that loading state is shown
      expect(screen.getByText(/signing in/i)).toBeInTheDocument();
      expect(submitButton).toBeDisabled();
      expect(emailInput).toBeDisabled();
      expect(passwordInput).toBeDisabled();

      // Wait for loading to complete
      await waitFor(() => {
        expect(screen.queryByText(/signing in/i)).not.toBeInTheDocument();
      });
    });
  });

  describe('Failed Login', () => {
    it('should display error message on authentication failure', async () => {
      const user = userEvent.setup();

      // Mock authentication error
      mockAuth.signInWithPassword.mockResolvedValue({
        error: { message: 'Invalid login credentials' },
        data: null,
      });

      render(<LoginForm />);

      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const submitButton = screen.getByRole('button', { name: /sign in/i });

      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'wrongpassword');
      await user.click(submitButton);

      // Wait for error message to appear
      await waitFor(() => {
        expect(screen.getByRole('alert')).toHaveTextContent('Invalid login credentials');
      });

      // Ensure router.push was not called
      expect(mockPush).not.toHaveBeenCalled();
    });

    it('should display fallback error message when error has no message', async () => {
      const user = userEvent.setup();

      // Mock authentication error without message
      mockAuth.signInWithPassword.mockResolvedValue({
        error: {},
        data: null,
      });

      render(<LoginForm />);

      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const submitButton = screen.getByRole('button', { name: /sign in/i });

      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'password123');
      await user.click(submitButton);

      await waitFor(() => {
        expect(screen.getByRole('alert')).toHaveTextContent('Invalid login credentials.');
      });
    });

    it('should handle unexpected errors during authentication', async () => {
      const user = userEvent.setup();

      // Mock unexpected error
      mockAuth.signInWithPassword.mockRejectedValue(new Error('Network error'));

      render(<LoginForm />);

      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const submitButton = screen.getByRole('button', { name: /sign in/i });

      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'password123');
      await user.click(submitButton);

      await waitFor(() => {
        expect(screen.getByRole('alert')).toHaveTextContent('Network error');
      });
    });

    it('should handle non-Error exceptions', async () => {
      const user = userEvent.setup();

      // Mock non-Error exception
      mockAuth.signInWithPassword.mockRejectedValue('String error');

      render(<LoginForm />);

      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const submitButton = screen.getByRole('button', { name: /sign in/i });

      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'password123');
      await user.click(submitButton);

      await waitFor(() => {
        expect(screen.getByRole('alert')).toHaveTextContent(
          'An unexpected error occurred. Please try again.'
        );
      });
    });

    it('should clear previous error messages on new submission', async () => {
      const user = userEvent.setup();

      // First submission with error
      mockAuth.signInWithPassword.mockResolvedValueOnce({
        error: { message: 'Invalid credentials' },
        data: null,
      });

      render(<LoginForm />);

      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const submitButton = screen.getByRole('button', { name: /sign in/i });

      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'wrongpassword');
      await user.click(submitButton);

      await waitFor(() => {
        expect(screen.getByRole('alert')).toHaveTextContent('Invalid credentials');
      });

      // Second submission with success
      mockAuth.signInWithPassword.mockResolvedValueOnce({
        error: null,
        data: { user: { id: '123' } },
      });

      await user.clear(passwordInput);
      await user.type(passwordInput, 'correctpassword');
      await user.click(submitButton);

      // Error message should be cleared
      await waitFor(() => {
        expect(screen.queryByRole('alert')).not.toBeInTheDocument();
      });
    });
  });

  describe('Form Validation', () => {
    it('should show validation error for invalid email format', async () => {
      const user = userEvent.setup();

      render(<LoginForm />);

      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const submitButton = screen.getByRole('button', { name: /sign in/i });

      // Enter invalid email
      await user.type(emailInput, 'invalid-email');
      await user.type(passwordInput, 'password123');
      await user.click(submitButton);

      // Check for validation error
      expect(screen.getByText('Please enter a valid email address.')).toBeInTheDocument();

      // Ensure API was not called
      expect(mockAuth.signInWithPassword).not.toHaveBeenCalled();
    });

    it('should show validation error for short password', async () => {
      const user = userEvent.setup();

      render(<LoginForm />);

      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const submitButton = screen.getByRole('button', { name: /sign in/i });

      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, '123'); // Too short
      await user.click(submitButton);

      expect(screen.getByText('Password must be at least 6 characters.')).toBeInTheDocument();
      expect(mockAuth.signInWithPassword).not.toHaveBeenCalled();
    });

    it('should show validation errors for empty fields', async () => {
      const user = userEvent.setup();

      render(<LoginForm />);

      const submitButton = screen.getByRole('button', { name: /sign in/i });

      // Submit empty form
      await user.click(submitButton);

      // Both validation errors should appear
      expect(screen.getByText('Please enter a valid email address.')).toBeInTheDocument();
      expect(screen.getByText('Password must be at least 6 characters.')).toBeInTheDocument();
      expect(mockAuth.signInWithPassword).not.toHaveBeenCalled();
    });

    it('should apply error styling to invalid fields', async () => {
      const user = userEvent.setup();

      render(<LoginForm />);

      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const submitButton = screen.getByRole('button', { name: /sign in/i });

      await user.type(emailInput, 'invalid-email');
      await user.type(passwordInput, '123');
      await user.click(submitButton);

      // Check that error styling is applied
      expect(emailInput).toHaveClass('border-red-500');
      expect(passwordInput).toHaveClass('border-red-500');
    });

    it('should remove validation errors when fields become valid', async () => {
      const user = userEvent.setup();

      render(<LoginForm />);

      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const submitButton = screen.getByRole('button', { name: /sign in/i });

      // First, trigger validation errors
      await user.click(submitButton);

      expect(screen.getByText('Please enter a valid email address.')).toBeInTheDocument();
      expect(screen.getByText('Password must be at least 6 characters.')).toBeInTheDocument();

      // Then fix the fields
      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'password123');

      // Validation errors should be cleared
      await waitFor(() => {
        expect(screen.queryByText('Please enter a valid email address.')).not.toBeInTheDocument();
        expect(
          screen.queryByText('Password must be at least 6 characters.')
        ).not.toBeInTheDocument();
      });

      // Error styling should be removed
      expect(emailInput).not.toHaveClass('border-red-500');
      expect(passwordInput).not.toHaveClass('border-red-500');
    });
  });

  describe('Form Accessibility', () => {
    it('should have proper form structure and labels', () => {
      render(<LoginForm />);

      // Check for proper form structure (form element exists)
      const formElement = screen.getByRole('button', { name: /sign in/i }).closest('form');
      expect(formElement).toBeInTheDocument();

      // Check for proper labels
      expect(screen.getByLabelText(/email/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/password/i)).toBeInTheDocument();

      // Check for submit button
      expect(screen.getByRole('button', { name: /sign in/i })).toBeInTheDocument();
    });

    it('should have proper ARIA attributes during loading', async () => {
      const user = userEvent.setup();

      mockAuth.signInWithPassword.mockImplementation(
        () => new Promise((resolve) => setTimeout(() => resolve({ error: null }), 100))
      );

      render(<LoginForm />);

      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const submitButton = screen.getByRole('button', { name: /sign in/i });

      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'password123');
      await user.click(submitButton);

      // Check aria-busy attribute
      expect(submitButton).toHaveAttribute('aria-busy', 'true');

      await waitFor(() => {
        expect(submitButton).toHaveAttribute('aria-busy', 'false');
      });
    });

    it('should have proper role for error messages', async () => {
      const user = userEvent.setup();

      mockAuth.signInWithPassword.mockResolvedValue({
        error: { message: 'Invalid credentials' },
        data: null,
      });

      render(<LoginForm />);

      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const submitButton = screen.getByRole('button', { name: /sign in/i });

      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'wrongpassword');
      await user.click(submitButton);

      await waitFor(() => {
        const errorElement = screen.getByRole('alert');
        expect(errorElement).toBeInTheDocument();
        expect(errorElement).toHaveTextContent('Invalid credentials');
      });
    });
  });
});
