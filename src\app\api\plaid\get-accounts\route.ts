import { NextResponse } from 'next/server';
import { createSupabaseServerClient } from '@/lib/supabase/server';

export async function GET() {
  try {
    const supabase = await createSupabaseServerClient();

    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const userId = user.id;

    const { data, error } = await supabase
      .from('financial_accounts')
      .select(
        'id, plaid_account_id, plaid_item_id, account_name, institution_name, current_balance, last_synced_at'
      )
      .eq('user_id', userId)
      .eq('is_active', true);

    if (error) {
      console.error('[PLAID_GET_ACCOUNTS_ERROR]', error);
      return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
    }

    return NextResponse.json({ accounts: data });
  } catch (error) {
    console.error('[PLAID_GET_ACCOUNTS_ERROR]', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}
