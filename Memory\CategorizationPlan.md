# Implementation Plan: Basic Categorization System

**Objective:** To build the core functionality for users to view their transactions, manage custom categories, and manually assign categories to transactions. This plan focuses on delivering a functional MVP, deferring advanced features like auto-categorization.

**A. Pre-computation Analysis**

*   **Schema Review:** An analysis of `supabase/schemas/04_transaction_categories.sql` and `supabase/schemas/05_user_categories.sql` confirms that `color` and `icon` fields are already present. The plan will focus on utilizing these fields in the UI rather than adding them.
*   **Component Scaffolding:** The plan will leverage the component names suggested in `dev-plan-01-foundation-mvp.md` (`TransactionsList`, `TransactionCard`, `CategorySelector`) for consistency.

**B. Implementation Phases**

The project is broken down into three main phases: Backend API development, Frontend UI implementation, and final integration.

```mermaid
graph TD
    subgraph Phase 1: Backend (API)
        A1[API: List Transactions] --> A2[API: CRUD for User Categories]
        A2 --> A3[API: Assign Category to Transaction]
    end

    subgraph Phase 2: Frontend (UI)
        B1[UI: Display Transactions] --> B2[UI: Manage Categories]
        B2 --> B3[UI: Categorization Interface]
    end

    subgraph Phase 3: Integration
        C1[Connect UI to APIs] --> C2[End-to-End Testing]
    end

    A3 --> C1
    B3 --> C1
```

---

### **Phase 1: Backend API Development**

This phase focuses on creating the necessary server-side endpoints to support the categorization features.

**Task 1.1: API Endpoint for Transaction Listing**
*   **Description:** Create a secure API endpoint that retrieves and lists transactions for the authenticated user. The endpoint should support pagination to handle large datasets efficiently.
*   **Subtasks:**
    1.  Create a new API route file: `src/app/api/transactions/get/route.ts`.
    2.  Implement logic to fetch transactions from the `transactions` table, joining with `financial_accounts` to show the account name.
    3.  Ensure the query is filtered by the authenticated `user_id`.
    4.  Implement pagination using query parameters (e.g., `page`, `pageSize`).
    5.  Return data in a structured format, including transaction details and account information.
*   **Delegation:** **Midlevel Developer** (Requires knowledge of Next.js API routes, Supabase queries, and data security).

**Task 1.2: API Endpoints for User Category Management (CRUD)**
*   **Description:** Develop a full set of CRUD endpoints for managing `user_categories`.
*   **Subtasks:**
    1.  **Create:** `src/app/api/categories/create/route.ts` - Endpoint to add a new category.
    2.  **Read:** `src/app/api/categories/get/route.ts` - Endpoint to list all of the user's custom categories.
    3.  **Update:** `src/app/api/categories/update/route.ts` - Endpoint to modify an existing category's `name`, `color`, or `icon`.
    4.  **Delete:** `src/app/api/categories/delete/route.ts` - Endpoint to remove a user's category.
*   **Delegation:** **Midlevel Developer** (Involves multiple endpoints and database operations).

**Task 1.3: API Endpoint for Transaction Categorization**
*   **Description:** Create an endpoint to assign or change the `user_category_id` for a specific transaction.
*   **Subtasks:**
    1.  Create a new API route file: `src/app/api/transactions/categorize/route.ts`.
    2.  The endpoint should accept a `transaction_id` and a `user_category_id`.
    3.  Implement logic to update the `user_category_id` field on the specified transaction record in the database.
    4.  Ensure the user owns both the transaction and the category to prevent unauthorized updates.
*   **Delegation:** **Junior Developer** (A single, well-defined update operation).

---

### **Phase 2: Frontend UI Implementation**

This phase focuses on building the user interface components for interacting with transactions and categories.

**Task 2.1: Transaction Display Components**
*   **Description:** Build the UI to display the list of imported transactions.
*   **Subtasks:**
    1.  Create a `TransactionsList` component (`src/components/transactions/TransactionsList.tsx`) to fetch and display a paginated list of transactions.
    2.  Create a `TransactionCard` component (`src/components/transactions/TransactionCard.tsx`) to display the details of a single transaction (merchant, date, amount).
    3.  Create a new page at `/dashboard/transactions` to host the `TransactionsList`.
*   **Delegation:** **Midlevel Developer** (Involves data fetching, state management, and component composition).

**Task 2.2: Category Management Interface**
*   **Description:** Create a simple UI for users to perform CRUD operations on their custom categories.
*   **Subtasks:**
    1.  Create a `CategoryManager` component (`src/components/categories/CategoryManager.tsx`).
    2.  Inside this component, build a form to create/edit a category (name, color picker, icon selector).
    3.  Display a list of existing user categories with buttons to edit or delete them.
    4.  Create a new page at `/dashboard/categories` to host the `CategoryManager`.
*   **Delegation:** **Midlevel Developer** (Requires form handling, state management, and multiple UI interactions).

**Task 2.3: Transaction Categorization UI**
*   **Description:** Implement the UI element that allows a user to assign a category to a transaction.
*   **Subtasks:**
    1.  Create a `CategorySelector` component (`src/components/categories/CategorySelector.tsx`).
    2.  This component will be a dropdown/select input populated with the user's custom categories.
    3.  Integrate the `CategorySelector` into the `TransactionCard` component.
    4.  When a user selects a category from the dropdown, it should trigger the API call to update the transaction.
*   **Delegation:** **Junior Developer** (A single, focused component with clear inputs and outputs).

---

### **Phase 3: Integration & Finalization**

**Task 3.1: Connect Frontend to Backend**
*   **Description:** Wire up all frontend components to their corresponding backend APIs.
*   **Subtasks:**
    1.  Connect `TransactionsList` to the `/api/transactions/get` endpoint.
    2.  Connect `CategoryManager` to the category CRUD endpoints.
    3.  Connect `CategorySelector` to the `/api/transactions/categorize` endpoint.
    4.  Implement loading states and error handling for all data-fetching operations.
*   **Delegation:** **Junior/Midlevel Developer** (Can be broken down by feature).

**Task 3.2: End-to-End Testing & Documentation**
*   **Description:** Ensure the system works as expected and document the new components.
*   **Subtasks:**
    1.  Manually test the full workflow: view transactions, create a category, assign it to a transaction, edit the category, delete it.
    2.  Write basic unit or integration tests for the new API endpoints.
    3.  Add comments and documentation for the new components and APIs.
*   **Delegation:** **Junior Developer**

---

### **C. Deferred Features**

To maintain focus on the MVP, the following advanced features are explicitly deferred to a later development phase:
*   **Rule-Based Auto-Categorization:** Automatically categorizing transactions based on merchant name, keywords, or user history.
*   **Category Hierarchy Management:** UI for managing parent/child relationships between categories.
*   **Bulk Categorization:** Selecting multiple transactions and assigning them to a single category in one action.