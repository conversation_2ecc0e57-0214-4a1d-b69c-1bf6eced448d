import { POST } from '../create/route';
import { createSupabaseServerClient } from '@/lib/supabase/server';
import { NextRequest } from 'next/server';

// Mock dependencies
jest.mock('@/lib/supabase/server', () => ({
  createSupabaseServerClient: jest.fn(),
}));

jest.mock('next/server', () => ({
  ...jest.requireActual('next/server'),
  NextResponse: {
    json: jest.fn((body, init) => ({
      status: init?.status || 200,
      json: () => Promise.resolve(body),
    })),
  },
}));

describe('POST /api/categories/create', () => {
  const mockUser = { id: 'test-user-id' };
  const mockCategoryData = {
    name: 'Test Category',
    color: '#FF5733',
    icon: 'shopping-cart',
    description: 'A test category for shopping',
  };

  const createMockRequest = (body: Record<string, unknown>) => {
    return new Request('http://localhost/api/categories/create', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(body),
    }) as NextRequest;
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('Success (201 Created) - creates category with all fields', async () => {
    const mockCreatedCategory = {
      id: 'cat-123',
      user_id: mockUser.id,
      name: 'Test Category',
      color: '#FF5733',
      icon: 'shopping-cart',
      description: 'A test category for shopping',
      is_active: true,
      created_at: '2025-06-08T20:00:00Z',
      updated_at: '2025-06-08T20:00:00Z',
    };

    const mockSupabase = {
      auth: {
        getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
      },
      from: jest.fn().mockReturnValue({
        insert: jest.fn().mockReturnValue({
          select: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({ data: mockCreatedCategory, error: null }),
          }),
        }),
      }),
    };

    (createSupabaseServerClient as jest.Mock).mockResolvedValue(mockSupabase);

    const req = createMockRequest(mockCategoryData);
    const res = await POST(req);
    const body = await res.json();

    expect(res.status).toBe(201);
    expect(body).toEqual({ category: mockCreatedCategory });
    expect(mockSupabase.auth.getUser).toHaveBeenCalledTimes(1);
    expect(mockSupabase.from).toHaveBeenCalledWith('user_categories');
    expect(mockSupabase.from().insert).toHaveBeenCalledWith({
      user_id: mockUser.id,
      name: 'Test Category',
      color: '#FF5733',
      icon: 'shopping-cart',
      description: 'A test category for shopping',
    });
  });

  test('Success (201 Created) - creates category with only required fields', async () => {
    const minimalCategoryData = { name: 'Minimal Category' };
    const mockCreatedCategory = {
      id: 'cat-124',
      user_id: mockUser.id,
      name: 'Minimal Category',
      color: null,
      icon: null,
      description: null,
      is_active: true,
      created_at: '2025-06-08T20:00:00Z',
      updated_at: '2025-06-08T20:00:00Z',
    };

    const mockSupabase = {
      auth: {
        getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
      },
      from: jest.fn().mockReturnValue({
        insert: jest.fn().mockReturnValue({
          select: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({ data: mockCreatedCategory, error: null }),
          }),
        }),
      }),
    };

    (createSupabaseServerClient as jest.Mock).mockResolvedValue(mockSupabase);

    const req = createMockRequest(minimalCategoryData);
    const res = await POST(req);
    const body = await res.json();

    expect(res.status).toBe(201);
    expect(body).toEqual({ category: mockCreatedCategory });
    expect(mockSupabase.from().insert).toHaveBeenCalledWith({
      user_id: mockUser.id,
      name: 'Minimal Category',
      color: null,
      icon: null,
      description: null,
    });
  });

  test('Unauthorized (401) - no user authenticated', async () => {
    const mockSupabase = {
      auth: {
        getUser: jest.fn().mockResolvedValue({ data: { user: null }, error: { message: 'Unauthorized' } }),
      },
    };

    (createSupabaseServerClient as jest.Mock).mockResolvedValue(mockSupabase);

    const req = createMockRequest(mockCategoryData);
    const res = await POST(req);
    const body = await res.json();

    expect(res.status).toBe(401);
    expect(body).toEqual({ error: 'Unauthorized' });
    expect(mockSupabase.auth.getUser).toHaveBeenCalledTimes(1);
  });

  test('Unauthorized (401) - auth error', async () => {
    const mockSupabase = {
      auth: {
        getUser: jest.fn().mockResolvedValue({ data: { user: null }, error: null }),
      },
    };

    (createSupabaseServerClient as jest.Mock).mockResolvedValue(mockSupabase);

    const req = createMockRequest(mockCategoryData);
    const res = await POST(req);
    const body = await res.json();

    expect(res.status).toBe(401);
    expect(body).toEqual({ error: 'Unauthorized' });
  });

  test('Missing Required Fields (400 Bad Request) - no name field', async () => {
    const invalidData = {
      color: '#FF5733',
      icon: 'shopping-cart',
      description: 'A test category without name',
    };

    const mockSupabase = {
      auth: {
        getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
      },
    };

    (createSupabaseServerClient as jest.Mock).mockResolvedValue(mockSupabase);

    const req = createMockRequest(invalidData);
    const res = await POST(req);
    const body = await res.json();

    expect(res.status).toBe(400);
    expect(body).toEqual({ error: 'Name is required and must be a non-empty string' });
  });

  test('Missing Required Fields (400 Bad Request) - empty name', async () => {
    const invalidData = {
      name: '',
      color: '#FF5733',
      icon: 'shopping-cart',
    };

    const mockSupabase = {
      auth: {
        getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
      },
    };

    (createSupabaseServerClient as jest.Mock).mockResolvedValue(mockSupabase);

    const req = createMockRequest(invalidData);
    const res = await POST(req);
    const body = await res.json();

    expect(res.status).toBe(400);
    expect(body).toEqual({ error: 'Name is required and must be a non-empty string' });
  });

  test('Missing Required Fields (400 Bad Request) - whitespace only name', async () => {
    const invalidData = {
      name: '   ',
      color: '#FF5733',
      icon: 'shopping-cart',
    };

    const mockSupabase = {
      auth: {
        getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
      },
    };

    (createSupabaseServerClient as jest.Mock).mockResolvedValue(mockSupabase);

    const req = createMockRequest(invalidData);
    const res = await POST(req);
    const body = await res.json();

    expect(res.status).toBe(400);
    expect(body).toEqual({ error: 'Name is required and must be a non-empty string' });
  });

  test('Invalid Field Types (400 Bad Request) - non-string color', async () => {
    const invalidData = {
      name: 'Test Category',
      color: 123,
      icon: 'shopping-cart',
    };

    const mockSupabase = {
      auth: {
        getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
      },
    };

    (createSupabaseServerClient as jest.Mock).mockResolvedValue(mockSupabase);

    const req = createMockRequest(invalidData);
    const res = await POST(req);
    const body = await res.json();

    expect(res.status).toBe(400);
    expect(body).toEqual({ error: 'Color must be a string' });
  });

  test('Invalid Field Types (400 Bad Request) - non-string icon', async () => {
    const invalidData = {
      name: 'Test Category',
      color: '#FF5733',
      icon: 456,
    };

    const mockSupabase = {
      auth: {
        getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
      },
    };

    (createSupabaseServerClient as jest.Mock).mockResolvedValue(mockSupabase);

    const req = createMockRequest(invalidData);
    const res = await POST(req);
    const body = await res.json();

    expect(res.status).toBe(400);
    expect(body).toEqual({ error: 'Icon must be a string' });
  });

  test('Invalid Field Types (400 Bad Request) - non-string description', async () => {
    const invalidData = {
      name: 'Test Category',
      color: '#FF5733',
      icon: 'shopping-cart',
      description: 789,
    };

    const mockSupabase = {
      auth: {
        getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
      },
    };

    (createSupabaseServerClient as jest.Mock).mockResolvedValue(mockSupabase);

    const req = createMockRequest(invalidData);
    const res = await POST(req);
    const body = await res.json();

    expect(res.status).toBe(400);
    expect(body).toEqual({ error: 'Description must be a string' });
  });

  test('Duplicate Name Error (409 Conflict) - category name already exists', async () => {
    const mockSupabase = {
      auth: {
        getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
      },
      from: jest.fn().mockReturnValue({
        insert: jest.fn().mockReturnValue({
          select: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({
              data: null,
              error: {
                code: '23505',
                message: 'duplicate key value violates unique constraint',
              },
            }),
          }),
        }),
      }),
    };

    (createSupabaseServerClient as jest.Mock).mockResolvedValue(mockSupabase);

    const req = createMockRequest(mockCategoryData);
    const res = await POST(req);
    const body = await res.json();

    expect(res.status).toBe(409);
    expect(body).toEqual({ error: 'A category with this name already exists' });
  });

  test('Database Error (500) - general database error', async () => {
    const mockSupabase = {
      auth: {
        getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
      },
      from: jest.fn().mockReturnValue({
        insert: jest.fn().mockReturnValue({
          select: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({
              data: null,
              error: {
                code: '42000',
                message: 'Database connection failed',
              },
            }),
          }),
        }),
      }),
    };

    (createSupabaseServerClient as jest.Mock).mockResolvedValue(mockSupabase);

    const req = createMockRequest(mockCategoryData);
    const res = await POST(req);
    const body = await res.json();

    expect(res.status).toBe(500);
    expect(body).toEqual({ error: 'Failed to create category' });
  });

  test('JSON Parse Error (500) - invalid JSON in request body', async () => {
    const mockSupabase = {
      auth: {
        getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
      },
    };

    (createSupabaseServerClient as jest.Mock).mockResolvedValue(mockSupabase);

    // Create a request with invalid JSON
    const req = new Request('http://localhost/api/categories/create', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: 'invalid json{',
    }) as NextRequest;

    const res = await POST(req);
    const body = await res.json();

    expect(res.status).toBe(500);
    expect(body).toEqual({ error: 'Internal Server Error' });
  });

  test('Name trimming - trims whitespace from name', async () => {
    const dataWithWhitespace = {
      name: '  Test Category  ',
      color: '#FF5733',
      icon: 'shopping-cart',
    };

    const mockCreatedCategory = {
      id: 'cat-125',
      user_id: mockUser.id,
      name: 'Test Category',
      color: '#FF5733',
      icon: 'shopping-cart',
      description: null,
      is_active: true,
      created_at: '2025-06-08T20:00:00Z',
      updated_at: '2025-06-08T20:00:00Z',
    };

    const mockSupabase = {
      auth: {
        getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
      },
      from: jest.fn().mockReturnValue({
        insert: jest.fn().mockReturnValue({
          select: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({ data: mockCreatedCategory, error: null }),
          }),
        }),
      }),
    };

    (createSupabaseServerClient as jest.Mock).mockResolvedValue(mockSupabase);

    const req = createMockRequest(dataWithWhitespace);
    const res = await POST(req);
    const body = await res.json();

    expect(res.status).toBe(201);
    expect(body).toEqual({ category: mockCreatedCategory });
    expect(mockSupabase.from().insert).toHaveBeenCalledWith({
      user_id: mockUser.id,
      name: 'Test Category', // Should be trimmed
      color: '#FF5733',
      icon: 'shopping-cart',
      description: null,
    });
  });
});