import { GET } from '../get-accounts/route';
import { createSupabaseServerClient } from '../../../../lib/supabase/server';

jest.mock('../../../../lib/supabase/server', () => ({
  createSupabaseServerClient: jest.fn(),
}));

describe('GET /api/plaid/get-accounts', () => {
  const mockUser = {
    id: 'test-user-id',
    email: '<EMAIL>',
  };

  const mockAccounts = [
    {
      id: 'account-id-1',
      plaid_account_id: 'plaid-account-id-1',
      plaid_item_id: 'plaid-item-id-1',
      account_name: 'Checking',
      institution_name: 'Test Bank',
      current_balance: 1000,
      last_synced_at: new Date().toISOString(),
    },
    {
      id: 'account-id-2',
      plaid_account_id: 'plaid-account-id-2',
      plaid_item_id: 'plaid-item-id-2',
      account_name: 'Savings',
      institution_name: 'Test Bank',
      current_balance: 5000,
      last_synced_at: new Date().toISOString(),
    },
  ];

  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('Success (200 OK)', async () => {
    const mockSelectResult = { data: mockAccounts, error: null };
    const mockEqActive = jest.fn().mockResolvedValue(mockSelectResult);
    const mockEqUser = jest.fn().mockReturnValue({ eq: mockEqActive });
    const mockSelect = jest.fn().mockReturnValue({ eq: mockEqUser });
    const mockFrom = jest.fn().mockReturnValue({ select: mockSelect });

    (createSupabaseServerClient as jest.Mock).mockResolvedValue({
      auth: {
        getUser: jest.fn().mockResolvedValue({
          data: { user: mockUser },
          error: null,
        }),
      },
      from: mockFrom,
    });

    const res = await GET();

    expect(res.status).toBe(200);
    const data = await res.json();
    expect(data.accounts).toEqual(mockAccounts);

    expect(mockFrom).toHaveBeenCalledWith('financial_accounts');
    expect(mockSelect).toHaveBeenCalledWith(
      'id, plaid_account_id, plaid_item_id, account_name, institution_name, current_balance, last_synced_at'
    );
    expect(mockEqUser).toHaveBeenCalledWith('user_id', mockUser.id);
    expect(mockEqActive).toHaveBeenCalledWith('is_active', true);
  });

  test('Unauthorized (401)', async () => {
    (createSupabaseServerClient as jest.Mock).mockResolvedValue({
      auth: {
        getUser: jest.fn().mockResolvedValue({
          data: { user: null },
          error: null,
        }),
      },
    });

    const res = await GET();

    expect(res.status).toBe(401);
    const data = await res.json();
    expect(data.error).toBe('Unauthorized');
  });

  test('Database Error (500)', async () => {
    const dbError = new Error('Database query failed');
    const mockSelectResult = { data: null, error: dbError };
    const mockEqActive = jest.fn().mockResolvedValue(mockSelectResult);
    const mockEqUser = jest.fn().mockReturnValue({ eq: mockEqActive });
    const mockSelect = jest.fn().mockReturnValue({ eq: mockEqUser });
    const mockFrom = jest.fn().mockReturnValue({ select: mockSelect });

    (createSupabaseServerClient as jest.Mock).mockResolvedValue({
      auth: {
        getUser: jest.fn().mockResolvedValue({
          data: { user: mockUser },
          error: null,
        }),
      },
      from: mockFrom,
    });

    const res = await GET();

    expect(res.status).toBe(500);
    const data = await res.json();
    expect(data.error).toBe('Internal Server Error');
    expect(mockFrom).toHaveBeenCalledWith('financial_accounts');
  });
});
