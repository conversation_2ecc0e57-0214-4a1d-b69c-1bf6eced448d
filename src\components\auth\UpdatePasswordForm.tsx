'use client';

import * as React from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { Card } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { createSupabaseBrowserClient } from '@/lib/supabase/client';
import { useRouter } from 'next/navigation';

const REDIRECT_DELAY_MS = 3000;

const supabase = createSupabaseBrowserClient();

// Type guard for error with message
function hasMessage(e: unknown): e is { message: string } {
  return (
    typeof e === 'object' &&
    e !== null &&
    'message' in e &&
    typeof (e as { message: unknown }).message === 'string'
  );
}

// Zod schema for form validation
const updatePasswordSchema = z
  .object({
    newPassword: z
      .string()
      .min(6, 'Password must be at least 6 characters')
      .min(1, 'New password is required'),
    confirmNewPassword: z.string().min(1, 'Please confirm your new password'),
  })
  .refine((data) => data.newPassword === data.confirmNewPassword, {
    message: 'Passwords do not match',
    path: ['confirmNewPassword'],
  });

type UpdatePasswordFormData = z.infer<typeof updatePasswordSchema>;

export function UpdatePasswordForm() {
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
  } = useForm<UpdatePasswordFormData>({
    resolver: zodResolver(updatePasswordSchema),
  });

  const [apiError, setApiError] = React.useState<string | null>(null);
  const [success, setSuccess] = React.useState(false);
  const router = useRouter();

  const onSubmit = async (data: UpdatePasswordFormData) => {
    setApiError(null);
    try {
      const { error } = await supabase.auth.updateUser({
        password: data.newPassword,
      });
      if (error) {
        setApiError(error.message || 'Failed to update password.');
        return;
      }
      setSuccess(true);
      reset();
      // Optionally redirect after a short delay
      setTimeout(() => {
        router.push('/login');
      }, REDIRECT_DELAY_MS);
    } catch (err: unknown) {
      let message = 'An unexpected error occurred. Please try again.';
      if (hasMessage(err)) {
        message = err.message;
      }
      setApiError(message);
    }
  };

  return (
    <Card className='max-w-md mx-auto p-6 mt-8'>
      <h2 className='text-2xl font-semibold mb-4 text-center'>Set a New Password</h2>
      {success ? (
        <div className='text-green-600 text-center space-y-2'>
          <p>Password updated successfully! You can now log in.</p>
          <Button asChild className='mt-2' variant='outline' aria-label='Go to login'>
            <a href='/login'>Go to Login</a>
          </Button>
          <p className='text-xs text-gray-500'>Redirecting to login in a few seconds...</p>
        </div>
      ) : (
        <form onSubmit={handleSubmit(onSubmit)} className='space-y-5'>
          <div>
            <Label htmlFor='newPassword'>New Password</Label>
            <Input
              id='newPassword'
              type='password'
              autoComplete='new-password'
              {...register('newPassword')}
              disabled={isSubmitting}
              className={errors.newPassword ? 'border-red-500' : ''}
            />
            {errors.newPassword && (
              <p className='text-red-600 text-sm mt-1'>{errors.newPassword.message}</p>
            )}
          </div>
          <div>
            <Label htmlFor='confirmNewPassword'>Confirm New Password</Label>
            <Input
              id='confirmNewPassword'
              type='password'
              autoComplete='new-password'
              {...register('confirmNewPassword')}
              disabled={isSubmitting}
              className={errors.confirmNewPassword ? 'border-red-500' : ''}
            />
            {errors.confirmNewPassword && (
              <p className='text-red-600 text-sm mt-1'>{errors.confirmNewPassword.message}</p>
            )}
          </div>
          {apiError && <div className='text-red-600 text-center text-sm'>{apiError}</div>}
          <Button type='submit' className='w-full' disabled={isSubmitting} aria-busy={isSubmitting}>
            {isSubmitting ? 'Updating...' : 'Set New Password'}
          </Button>
        </form>
      )}
    </Card>
  );
}

export default UpdatePasswordForm;
