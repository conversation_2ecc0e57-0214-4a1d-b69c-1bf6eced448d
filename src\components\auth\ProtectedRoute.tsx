import React, { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from './AuthProvider';

type ProtectedRouteProps = {
  children: React.ReactNode;
};

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {
  const { user, isLoading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!isLoading && !user) {
      router.replace('/login');
    }
  }, [isLoading, user, router]);

  if (isLoading) {
    return (
      <div className='flex items-center justify-center min-h-screen'>
        <span role='status' aria-live='polite'>
          Loading...
        </span>
      </div>
    );
  }

  if (!user) {
    // Optionally render nothing while redirecting
    return null;
  }

  return <>{children}</>;
};

export default ProtectedRoute;
