import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import AccountsList from '../AccountsList';

const mockAccounts = [
  {
    id: '1',
    account_name: 'Checking Account',
    institution_name: 'Bank A',
    current_balance: 1000,
    last_synced_at: '2025-06-01T12:00:00Z',
    plaid_item_id: 'item_1',
  },
  {
    id: '2',
    account_name: 'Savings Account',
    institution_name: 'Bank B',
    current_balance: 5000,
    last_synced_at: '2025-06-01T12:00:00Z',
    plaid_item_id: 'item_2',
  },
];

describe('AccountsList', () => {
  beforeEach(() => {
    global.fetch = jest.fn();
  });

  afterEach(() => {
    jest.resetAllMocks();
  });

  test('renders AccountCard components when accounts are present', async () => {
    (global.fetch as jest.Mock).mockResolvedValue({
      ok: true,
      json: async () => ({ accounts: mockAccounts }),
    });

    render(<AccountsList />);

    await waitFor(() => {
      expect(screen.getByText('Checking Account')).toBeInTheDocument();
      expect(screen.getByText('Savings Account')).toBeInTheDocument();
    });

    expect(screen.getAllByRole('heading', { level: 2 })).toHaveLength(2);
  });

  test('renders "No accounts connected" message when accounts array is empty', async () => {
    (global.fetch as jest.Mock).mockResolvedValue({
      ok: true,
      json: async () => ({ accounts: [] }),
    });

    render(<AccountsList />);

    await waitFor(() => {
      expect(
        screen.getByText((content) => content.includes('No connected financial accounts found'))
      ).toBeInTheDocument();
    });
  });

  test('renders loading state initially', () => {
    (global.fetch as jest.Mock).mockImplementation(() => new Promise(() => {}));

    render(<AccountsList />);

    expect(screen.getByText('Loading accounts...')).toBeInTheDocument();
  });
});
