import { POST } from '../create-link-token/route';
import { plaidClient } from '../../../../lib/plaid';
import { createSupabaseServerClient } from '../../../../lib/supabase/server';

jest.mock('../../../../lib/plaid', () => ({
  plaidClient: {
    linkTokenCreate: jest.fn(),
  },
}));

jest.mock('../../../../lib/supabase/server', () => ({
  createSupabaseServerClient: jest.fn(),
}));

describe('POST /api/plaid/create-link-token', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('Success (200 OK)', async () => {
    // Simulate an authenticated user.
    (createSupabaseServerClient as jest.Mock).mockResolvedValue({
      auth: {
        getUser: jest.fn().mockResolvedValue({ data: { user: { id: 'user-id' } }, error: null }),
      },
    });
    // Mock the plaid client to resolve with a link token.
    (plaidClient.linkTokenCreate as jest.Mock).mockResolvedValue({
      data: { link_token: 'test_token' },
    });

    const res = await POST();
    expect(res.status).toBe(200);
    const data = await res.json();
    expect(data.link_token).toBe('test_token');
  });

  test('Unauthorized (401)', async () => {
    // Simulate missing authentication.
    (createSupabaseServerClient as jest.Mock).mockResolvedValue({
      auth: {
        getUser: jest.fn().mockResolvedValue({ data: { user: null }, error: null }),
      },
    });

    const res = await POST();
    expect(res.status).toBe(401);
    const data = await res.json();
    expect(data.error).toBe('Unauthorized');
  });

  test('Plaid Client Error (500)', async () => {
    // Simulate an authenticated user.
    (createSupabaseServerClient as jest.Mock).mockResolvedValue({
      auth: {
        getUser: jest.fn().mockResolvedValue({ data: { user: { id: 'user-id' } }, error: null }),
      },
    });
    // Cause the plaid client to throw an error.
    (plaidClient.linkTokenCreate as jest.Mock).mockRejectedValue(new Error('Mock Plaid Error'));

    const res = await POST();
    expect(res.status).toBe(500);
    const data = await res.json();
    expect(data.error).toBe('Internal Server Error');
  });
});