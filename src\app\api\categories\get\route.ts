import { NextResponse } from 'next/server';
import { createSupabaseServerClient } from '@/lib/supabase/server';

export async function GET() {
  const supabase = await createSupabaseServerClient();

  // Check authentication
  const {
    data: { user },
    error,
  } = await supabase.auth.getUser();

  if (error || !user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  const userId = user.id;

  try {
    // Fetch all active categories for the user
    const { data: categories, error: fetchError } = await supabase
      .from('user_categories')
      .select(
        `
        id,
        name,
        description,
        base_category_id,
        icon,
        color,
        is_active,
        created_at,
        updated_at
      `
      )
      .eq('user_id', userId)
      .eq('is_active', true)
      .order('name', { ascending: true });

    if (fetchError) {
      console.error('Error fetching categories:', fetchError);
      return NextResponse.json({ error: 'Failed to fetch categories' }, { status: 500 });
    }

    return NextResponse.json({
      categories: categories || [],
    });
  } catch (error) {
    console.error('[CATEGORIES_GET_ERROR]', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}
