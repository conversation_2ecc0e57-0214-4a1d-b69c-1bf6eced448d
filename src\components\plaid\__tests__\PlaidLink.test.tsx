import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import PlaidLink from '../PlaidLink';
import { usePlaidLink, PlaidLinkOnSuccessMetadata, PlaidLinkOnExit, PlaidLinkError } from 'react-plaid-link';

// Mock the usePlaidLink hook
jest.mock('react-plaid-link', () => ({
  usePlaidLink: jest.fn(),
}));

// Mock global fetch
global.fetch = jest.fn();

const mockUsePlaidLink = usePlaidLink as jest.Mock;

describe('PlaidLink Component', () => {
  const mockOpen = jest.fn();
  const mockOnSuccess = jest.fn();
  const mockOnExit = jest.fn();
  const userId = 'test-user-id';

  beforeEach(() => {
    jest.clearAllMocks();

    // Default mock for usePlaidLink
    mockUsePlaidLink.mockReturnValue({
      open: mockOpen,
      ready: true,
      isLoaded: true,
    });

    // Default mock for create-link-token fetch call
    (global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => ({ link_token: 'mock-link-token' }),
    });
  });

  // Test Case 1: Initial Rendering and Button Click
  test('renders the connect button and calls open on click', async () => {
    render(<PlaidLink userId={userId} onSuccess={mockOnSuccess} onExit={mockOnExit} />);

    // Wait for the link token to be fetched and the button to be enabled
    const connectButton = await screen.findByRole('button', { name: /connect a bank account/i });
    expect(connectButton).toBeInTheDocument();
    expect(connectButton).not.toBeDisabled();

    fireEvent.click(connectButton);

    expect(mockOpen).toHaveBeenCalledTimes(1);
  });

  // Test Case 2: Successful Token Exchange on Plaid Success
  test('handles successful token exchange on Plaid success', async () => {
    // Arrange
    const public_token = 'test-public-token';
    const metadata = { institution: { name: 'Test Bank', institution_id: 'ins_123' }, accounts: [], link_session_id: 'session-id' };

    // Mock the fetch call for token exchange
    (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ success: true }),
    });
    
    // Capture the onSuccess callback passed to usePlaidLink
    let plaidOnSuccess: (public_token: string, metadata: PlaidLinkOnSuccessMetadata) => void;
    mockUsePlaidLink.mockImplementation(config => {
        plaidOnSuccess = config.onSuccess;
        return {
            open: mockOpen,
            ready: true,
            isLoaded: true,
        };
    });

    render(<PlaidLink userId={userId} onSuccess={mockOnSuccess} onExit={mockOnExit} />);
    
    // Act
    // Simulate Plaid's onSuccess callback
    await waitFor(() => {
        expect(plaidOnSuccess).toBeDefined();
        plaidOnSuccess(public_token, metadata);
    });

    // Assert
    await waitFor(() => {
        expect(global.fetch).toHaveBeenCalledWith('/api/plaid/exchange-public-token', expect.any(Object));
        expect(mockOnSuccess).toHaveBeenCalledTimes(1);
        expect(mockOnSuccess).toHaveBeenCalledWith(public_token, metadata);
    });
  });

  // Test Case 3: Handling API Error during Token Exchange
  test('handles API error during token exchange', async () => {
    const public_token = 'test-public-token';
    const metadata = { institution: { name: 'Test Bank', institution_id: 'ins_123' }, accounts: [], link_session_id: 'session-id' };
    const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

    // Mock the fetch call to fail
    (global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: false,
      statusText: 'Internal Server Error',
    });

    let plaidOnSuccess: (public_token: string, metadata: PlaidLinkOnSuccessMetadata) => void;
    mockUsePlaidLink.mockImplementation(config => {
        plaidOnSuccess = config.onSuccess;
        return {
            open: mockOpen,
            ready: true,
            isLoaded: true,
        };
    });

    render(<PlaidLink userId={userId} onSuccess={mockOnSuccess} onExit={mockOnExit} />);

    await waitFor(() => {
        expect(plaidOnSuccess).toBeDefined();
        plaidOnSuccess(public_token, metadata);
    });

    await waitFor(() => {
      expect(consoleErrorSpy).toHaveBeenCalledWith(
        'Error exchanging public token:',
        new Error('Failed to exchange public token: Internal Server Error')
      );
    });
    
    expect(mockOnSuccess).not.toHaveBeenCalled();
    consoleErrorSpy.mockRestore();
  });

  // Test Case 4: Handling Plaid's onExit Event
  test("calls the onExit prop when Plaid's onExit is triggered", async () => {
    const error: PlaidLinkError = {
        error_type: 'EXIT',
        error_code: 'USER_EXIT',
        error_message: 'User exited.',
        display_message: '',
    };
    const metadata: Parameters<PlaidLinkOnExit>[1] = { status: 'user_exit', link_session_id: 'session-id', request_id: '123', institution: { name: 'Test', institution_id: 'ins_123'} };

    let plaidOnExit: PlaidLinkOnExit;
    mockUsePlaidLink.mockImplementation(config => {
        plaidOnExit = config.onExit;
        return {
            open: mockOpen,
            ready: true,
            isLoaded: true,
        };
    });

    render(<PlaidLink userId={userId} onSuccess={mockOnSuccess} onExit={mockOnExit} />);

    await waitFor(() => {
        expect(plaidOnExit).toBeDefined();
        plaidOnExit(error, metadata);
    });

    await waitFor(() => {
        expect(mockOnExit).toHaveBeenCalledTimes(1);
        expect(mockOnExit).toHaveBeenCalledWith(error, metadata);
    });
  });
});