'use client';

import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';

interface Category {
  id: string;
  name: string;
  description: string | null;
  color: string | null;
  icon: string | null;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

interface CategoryFormData {
  name: string;
  description: string;
  color: string;
  icon: string;
}

const PREDEFINED_COLORS = [
  '#ef4444',
  '#f97316',
  '#f59e0b',
  '#eab308',
  '#84cc16',
  '#22c55e',
  '#10b981',
  '#14b8a6',
  '#06b6d4',
  '#0ea5e9',
  '#3b82f6',
  '#6366f1',
  '#8b5cf6',
  '#a855f7',
  '#d946ef',
  '#ec4899',
  '#f43f5e',
];

const PREDEFINED_ICONS = [
  '🍔',
  '🛒',
  '⛽',
  '🏠',
  '💡',
  '🚗',
  '🎬',
  '👕',
  '💊',
  '🎓',
  '✈️',
  '🏥',
  '💰',
  '🎮',
  '📱',
  '☕',
  '🍕',
  '🎵',
  '📚',
  '🏋️',
];

/**
 * CategoryManager component allows users to view, create, edit, and delete categories.
 * It provides a form for category details including name, description, color, and icon.
 * Handles loading and submission states, and displays validation errors.
 *
 * @component
 * @returns {JSX.Element} The rendered category management interface
 */
export default function CategoryManager() {
  const [categories, setCategories] = useState<Category[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [editingCategory, setEditingCategory] = useState<Category | null>(null);
  const [error, setError] = useState<string | null>(null);

  const form = useForm<CategoryFormData>({
    defaultValues: {
      name: '',
      description: '',
      color: PREDEFINED_COLORS[0],
      icon: PREDEFINED_ICONS[0],
    },
  });

  // Fetch categories
  const fetchCategories = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/categories/get');

      if (!response.ok) {
        throw new Error('Failed to fetch categories');
      }

      const data = await response.json();
      setCategories(data.categories || []);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch categories');
    } finally {
      setIsLoading(false);
    }
  };

  // Create or update category
  const onSubmit = async (data: CategoryFormData) => {
    try {
      setIsSubmitting(true);
      setError(null);

      const url = editingCategory ? '/api/categories/update' : '/api/categories/create';
      const method = editingCategory ? 'PUT' : 'POST';

      const payload = editingCategory ? { category_id: editingCategory.id, ...data } : data;

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to save category');
      }

      // Reset form and refresh categories
      form.reset();
      setEditingCategory(null);
      await fetchCategories();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to save category');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Delete category
  const handleDelete = async (category: Category) => {
    if (!confirm(`Are you sure you want to delete "${category.name}"?`)) {
      return;
    }

    try {
      setError(null);
      const response = await fetch('/api/categories/delete', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ category_id: category.id }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete category');
      }

      await fetchCategories();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete category');
    }
  };

  // Edit category
  const handleEdit = (category: Category) => {
    setEditingCategory(category);
    form.reset({
      name: category.name,
      description: category.description || '',
      color: category.color || PREDEFINED_COLORS[0],
      icon: category.icon || PREDEFINED_ICONS[0],
    });
  };

  // Cancel editing
  const handleCancel = () => {
    setEditingCategory(null);
    form.reset();
  };

  useEffect(() => {
    fetchCategories();
  }, []);

  if (isLoading) {
    return (
      <div className='flex justify-center items-center p-8'>
        <LoadingSpinner />
      </div>
    );
  }

  return (
    <div className='space-y-6'>
      {/* Error Display */}
      {error && (
        <div className='bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded'>
          {error}
        </div>
      )}

      {/* Category Form */}
      <Card>
        <CardHeader>
          <CardTitle>{editingCategory ? 'Edit Category' : 'Create New Category'}</CardTitle>
          <CardDescription>
            {editingCategory
              ? 'Update the details of your category'
              : 'Add a new custom spending category'}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-4'>
              <FormField
                control={form.control}
                name='name'
                rules={{ required: 'Category name is required' }}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Name</FormLabel>
                    <FormControl>
                      <Input placeholder='e.g., Coffee Shops' {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name='description'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description (Optional)</FormLabel>
                    <FormControl>
                      <Input placeholder='e.g., Daily coffee purchases' {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name='color'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Color</FormLabel>
                    <FormControl>
                      <div className='space-y-2'>
                        <div className='flex flex-wrap gap-2'>
                          {PREDEFINED_COLORS.map((color) => (
                            <button
                              key={color}
                              type='button'
                              className={`w-8 h-8 rounded-full border-2 ${
                                field.value === color ? 'border-gray-800' : 'border-gray-300'
                              }`}
                              style={{ backgroundColor: color }}
                              onClick={() => field.onChange(color)}
                            />
                          ))}
                        </div>
                        <Input
                          type='color'
                          value={field.value}
                          onChange={field.onChange}
                          className='w-20 h-10'
                        />
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name='icon'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Icon</FormLabel>
                    <FormControl>
                      <div className='space-y-2'>
                        <div className='flex flex-wrap gap-2'>
                          {PREDEFINED_ICONS.map((icon) => (
                            <button
                              key={icon}
                              type='button'
                              className={`w-10 h-10 text-xl border rounded ${
                                field.value === icon
                                  ? 'border-blue-500 bg-blue-50'
                                  : 'border-gray-300 hover:border-gray-400'
                              }`}
                              onClick={() => field.onChange(icon)}
                            >
                              {icon}
                            </button>
                          ))}
                        </div>
                        <Input
                          placeholder='Or enter custom emoji'
                          value={field.value}
                          onChange={field.onChange}
                          className='w-32'
                        />
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className='flex gap-2'>
                <Button type='submit' disabled={isSubmitting}>
                  {isSubmitting ? (
                    <>
                      <LoadingSpinner />
                      {editingCategory ? 'Updating...' : 'Creating...'}
                    </>
                  ) : editingCategory ? (
                    'Update Category'
                  ) : (
                    'Create Category'
                  )}
                </Button>
                {editingCategory && (
                  <Button type='button' variant='outline' onClick={handleCancel}>
                    Cancel
                  </Button>
                )}
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>

      {/* Categories List */}
      <Card>
        <CardHeader>
          <CardTitle>Your Categories</CardTitle>
          <CardDescription>Manage your custom spending categories</CardDescription>
        </CardHeader>
        <CardContent>
          {categories.length === 0 ? (
            <p className='text-gray-500 text-center py-8'>
              No categories created yet. Create your first category above!
            </p>
          ) : (
            <div className='space-y-3'>
              {categories.map((category) => (
                <div
                  key={category.id}
                  className='flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50'
                >
                  <div className='flex items-center gap-3'>
                    <div
                      className='w-6 h-6 rounded-full flex items-center justify-center text-white text-sm'
                      style={{ backgroundColor: category.color || '#6b7280' }}
                    >
                      {category.icon || '📁'}
                    </div>
                    <div>
                      <h3 className='font-medium'>{category.name}</h3>
                      {category.description && (
                        <p className='text-sm text-gray-600'>{category.description}</p>
                      )}
                    </div>
                  </div>
                  <div className='flex gap-2'>
                    <Button variant='outline' size='sm' onClick={() => handleEdit(category)}>
                      Edit
                    </Button>
                    <Button variant='destructive' size='sm' onClick={() => handleDelete(category)}>
                      Delete
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
