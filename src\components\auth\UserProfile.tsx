'use client';

import React, { useEffect, useState } from 'react';
import { useAuth } from './AuthProvider';
import { createSupabaseBrowserClient } from '@/lib/supabase/client';
import { useForm } from 'react-hook-form';

const supabase = createSupabaseBrowserClient();
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useRouter } from 'next/navigation';
import { Card, CardHeader, CardTitle, CardContent, CardFooter } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';

type Profile = {
  id: string;
  full_name: string | null;
  email?: string; // email is already on user object from useAuth()
};

const fullNameSchema = z.object({
  full_name: z
    .string()
    .min(2, 'Full name must be at least 2 characters')
    .max(50, 'Full name must be at most 50 characters')
    .optional()
    .or(z.literal('')), // Allows empty string to clear the name
});

type FullNameForm = z.infer<typeof fullNameSchema>;

export default function UserProfile() {
  const { user } = useAuth(); // user object from Supabase already has email
  const router = useRouter();
  // Supabase client is now created at the module top-level for efficiency.

  const [profile, setProfile] = useState<Omit<Profile, 'email'> | null>(null); // Store only what's fetched from 'profiles' table
  const [profileLoading, setProfileLoading] = useState(true);
  const [profileError, setProfileError] = useState<string | null>(null);

  const [updateLoading, setUpdateLoading] = useState(false);
  const [updateError, setUpdateError] = useState<string | null>(null);
  const [updateSuccess, setUpdateSuccess] = useState<string | null>(null);

  // Fetch profile data (specifically full_name)
  useEffect(() => {
    if (!user) {
      setProfile(null);
      setProfileLoading(false);
      return;
    }
    setProfileLoading(true);
    setProfileError(null);

    const fetchProfile = async () => {
      try {
        const { data, error } = await supabase
          .from('profiles')
          .select('id, full_name') // Only select what's needed from 'profiles'
          .eq('id', user.id)
          .single();

        if (error) {
          // It's possible a profile record doesn't exist yet if signup doesn't auto-create one
          // or if there was an issue. Handle this more gracefully.
          if (error.code === 'PGRST116') {
            // PGRST116: "Searched for a single row, but found no rows"
            setProfile({ id: user.id, full_name: null }); // Assume profile can be created/updated
            console.warn('No profile record found for user, allowing creation/update.');
          } else {
            setProfileError(`Failed to load profile: ${error.message}`);
            setProfile(null);
          }
        } else if (data) {
          setProfile(data);
        } else {
          setProfile({ id: user.id, full_name: null }); // Handle case where data is null but no error
        }
      } catch (e: unknown) {
        const errorMessage =
          e instanceof Error ? e.message : 'An unexpected error occurred while fetching profile.';
        setProfileError(errorMessage);
        setProfile(null);
      } finally {
        setProfileLoading(false);
      }
    };

    fetchProfile();
  }, [user]); // Removed supabase from dependency array as it is stable

  const {
    register,
    handleSubmit,
    formState: { errors, isDirty },
    reset,
  } = useForm<FullNameForm>({
    resolver: zodResolver(fullNameSchema),
    defaultValues: {
      full_name: '', // Initialize empty, will be set by useEffect
    },
  });

  // Keep form in sync with profile.full_name
  useEffect(() => {
    if (profile) {
      reset({ full_name: profile.full_name ?? '' });
    } else {
      reset({ full_name: '' }); // Reset to empty if profile is null
    }
  }, [profile, reset]);

  const onSubmitFullName = async (values: FullNameForm) => {
    if (!user) return;
    setUpdateLoading(true);
    setUpdateError(null);
    setUpdateSuccess(null);

    try {
      // Upsert allows creating the profile if it doesn't exist, or updating if it does.
      // This is safer if the profile might not have been created during signup.
      const { error } = await supabase
        .from('profiles')
        .update({ full_name: values.full_name || null }) // Ensure null if empty string
        .eq('id', user.id)
        .select() // Important to get returned data for optimistic update or confirmation
        .single(); // Assuming one profile per user

      if (error) {
        setUpdateError(`Failed to update full name: ${error.message}`);
      } else {
        setUpdateSuccess('Full name updated successfully.');
        // Optimistically update local profile state
        setProfile((prev) =>
          prev
            ? { ...prev, full_name: values.full_name || null }
            : { id: user.id, full_name: values.full_name || null }
        );
        reset({ full_name: values.full_name || '' }); // Reset form to new values and clear dirty state
      }
    } catch (e: unknown) {
      const errorMessage =
        e instanceof Error ? e.message : 'An unexpected error occurred during update.';
      setUpdateError(errorMessage);
    } finally {
      setUpdateLoading(false);
    }
  };

  const handleLogout = async () => {
    setUpdateError(null); // Clear any previous update errors
    setUpdateSuccess(null); // Clear any previous update successes
    try {
      const { error } = await supabase.auth.signOut();
      if (error) {
        setUpdateError(`Logout failed: ${error.message}`);
        return;
      }
      router.push('/login'); // Redirect after successful logout
    } catch (e: unknown) {
      const errorMessage =
        e instanceof Error ? e.message : 'An unexpected error occurred during logout.';
      setUpdateError(errorMessage);
    }
  };

  if (!user) {
    // Should be handled by ProtectedRoute, but good for robustness
    return <p>Loading user...</p>; // Or redirect, though ProtectedRoute should handle this
  }

  return (
    <div className='flex justify-center items-center min-h-[60vh] px-2'>
      <Card className='w-full max-w-md shadow-lg'>
        <CardHeader>
          <CardTitle>User Profile</CardTitle>
        </CardHeader>
        <CardContent>
          {profileLoading ? (
            <div className='text-center text-muted-foreground py-4'>Loading profile...</div>
          ) : profileError ? (
            <div className='text-center text-destructive py-4' role='alert'>
              {profileError}
            </div>
          ) : (
            <div className='space-y-4'>
              <div>
                <Label className='block mb-1'>Email</Label>
                <div className='bg-muted rounded px-3 py-2 text-sm'>
                  {user.email} {/* Use email from useAuth().user directly */}
                </div>
              </div>
              <form
                className='space-y-2'
                onSubmit={handleSubmit(onSubmitFullName)}
                autoComplete='off'
              >
                <div>
                  {' '}
                  {/* Added div for better structure with label and input */}
                  <Label htmlFor='full_name' className='block mb-1'>
                    {' '}
                    {/* Added mb-1 for spacing */}
                    Full Name
                  </Label>
                  <Input
                    id='full_name'
                    {...register('full_name')}
                    placeholder='Enter your full name'
                    disabled={updateLoading}
                    className={errors.full_name ? 'border-destructive' : ''}
                  />
                  {errors.full_name && (
                    <div className='text-destructive text-xs mt-1'>
                      {' '}
                      {/* Added mt-1 for spacing */}
                      {errors.full_name.message}
                    </div>
                  )}
                </div>
                <Button
                  type='submit'
                  className='mt-2 w-full' // Ensure this class applies as intended
                  disabled={updateLoading || !isDirty}
                >
                  {updateLoading ? 'Updating...' : 'Update Full Name'}
                </Button>
                {updateError && (
                  <div className='text-destructive text-xs mt-1' role='alert'>
                    {' '}
                    {/* Added role="alert" */}
                    {updateError}
                  </div>
                )}
                {updateSuccess && (
                  <div className='text-green-600 text-xs mt-1'>
                    {' '}
                    {/* Changed from text-success to text-green-600 */}
                    {updateSuccess}
                  </div>
                )}
              </form>
            </div>
          )}
        </CardContent>
        <CardFooter className='flex flex-col gap-2 pt-4'>
          {' '}
          {/* Added pt-4 for spacing */}
          <Button
            variant='outline'
            className='w-full'
            onClick={handleLogout}
            disabled={updateLoading} // Consider if logout should be disabled during profile update
          >
            Logout
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}
