import React from 'react';

/**
 * A simple, reusable loading spinner component.
 * Uses SVG and Tailwind for animation.
 *
 * @param className Optional className for custom styling.
 */
export const LoadingSpinner = ({ className }: { className?: string }) => (
  <span
    role='status'
    aria-label='Loading'
    className={`inline-flex items-center justify-center ${className ?? ''}`}
  >
    <svg
      xmlns='http://www.w3.org/2000/svg'
      width='24'
      height='24'
      viewBox='0 0 24 24'
      fill='none'
      stroke='currentColor'
      strokeWidth='2'
      strokeLinecap='round'
      strokeLinejoin='round'
      className='animate-spin'
    >
      <path d='M21 12a9 9 0 1 1-6.219-8.56' />
    </svg>
    <span className='sr-only'>Loading...</span>
  </span>
);

export default LoadingSpinner;
