import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClient } from '@/lib/supabase/server';

export async function GET(request: NextRequest) {
  const supabase = await createSupabaseServerClient();

  // Check authentication
  const {
    data: { user },
    error,
  } = await supabase.auth.getUser();

  if (error || !user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  const userId = user.id;

  // Parse query parameters for pagination
  const { searchParams } = new URL(request.url);
  const page = parseInt(searchParams.get('page') || '1', 10);
  const pageSize = parseInt(searchParams.get('pageSize') || '20', 10);

  // Validate pagination parameters
  if (page < 1 || pageSize < 1 || pageSize > 100) {
    return NextResponse.json(
      {
        error:
          'Invalid pagination parameters. Page must be >= 1 and pageSize must be between 1 and 100.',
      },
      { status: 400 }
    );
  }

  const offset = (page - 1) * pageSize;

  try {
    // Get total count for pagination
    const { count, error: countError } = await supabase
      .from('transactions')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', userId);

    if (countError) {
      console.error('Error counting transactions:', countError);
      return NextResponse.json({ error: 'Failed to count transactions' }, { status: 500 });
    }

    // Fetch transactions with financial account data
    const { data: transactions, error: transactionsError } = await supabase
      .from('transactions')
      .select(
        `
        id,
        amount,
        currency_code,
        transaction_date,
        authorized_date,
        posted_date,
        merchant_name,
        description,
        category_id,
        user_category_id,
        plaid_category,
        plaid_category_detailed,
        transaction_type,
        location,
        is_pending,
        is_recurring,
        status,
        tags,
        notes,
        created_at,
        updated_at,
        financial_accounts!inner (
          id,
          account_name,
          institution_name,
          account_type,
          account_subtype,
          mask
        )
      `
      )
      .eq('user_id', userId)
      .order('transaction_date', { ascending: false })
      .order('created_at', { ascending: false })
      .range(offset, offset + pageSize - 1);

    if (transactionsError) {
      console.error('Error fetching transactions:', transactionsError);
      return NextResponse.json({ error: 'Failed to fetch transactions' }, { status: 500 });
    }

    // Calculate pagination metadata
    const totalCount = count || 0;
    const totalPages = Math.ceil(totalCount / pageSize);

    return NextResponse.json({
      transactions: transactions || [],
      pagination: {
        currentPage: page,
        pageSize,
        totalCount,
        totalPages,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      },
    });
  } catch (error) {
    console.error('[TRANSACTIONS_GET_ERROR]', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}
