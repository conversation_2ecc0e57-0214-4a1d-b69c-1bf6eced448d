'use client';

import React, { useState } from 'react';

interface AccountCardProps {
  account: {
    id: string;
    account_name: string;
    institution_name: string;
    current_balance: number | null;
    last_synced_at: string | null;
    plaid_item_id: string;
  };
}

export default function AccountCard({ account }: AccountCardProps) {
  const [isSyncing, setIsSyncing] = useState(false);
  const [message, setMessage] = useState<string | null>(null);

  const handleSync = async () => {
    setIsSyncing(true);
    setMessage(null);
    try {
      const response = await fetch('/api/plaid/sync-transactions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ item_id: account.plaid_item_id }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Sync failed');
      }

      setMessage('Sync successful!');
    } catch (error) {
      setMessage(`Error: ${error instanceof Error ? error.message : 'An unknown error occurred'}`);
    } finally {
      setIsSyncing(false);
    }
  };

  return (
    <div className='border rounded p-4 shadow-md mb-4 w-full max-w-md'>
      <h2 className='text-xl font-semibold'>{account.account_name}</h2>
      <p className='text-gray-600'>Institution: {account.institution_name}</p>
      <p className='text-gray-600'>
        Current Balance:{' '}
        {account.current_balance !== null ? `$${account.current_balance.toFixed(2)}` : 'N/A'}
      </p>
      <p className='text-gray-600'>
        Last Synced:{' '}
        {account.last_synced_at ? new Date(account.last_synced_at).toLocaleString() : 'Never'}
      </p>
      <button
        onClick={handleSync}
        disabled={isSyncing}
        className='mt-3 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50'
      >
        {isSyncing ? 'Syncing...' : 'Sync Now'}
      </button>
      {message && <p className='mt-2 text-sm'>{message}</p>}
    </div>
  );
}
