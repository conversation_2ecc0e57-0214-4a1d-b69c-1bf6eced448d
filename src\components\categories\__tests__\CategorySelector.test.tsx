import React from 'react';
import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import CategorySelector from '../CategorySelector';

describe('CategorySelector', () => {
  const categoriesMock = [
    { id: '1', name: 'Food' },
    { id: '2', name: 'Transport' },
    { id: '3', name: 'Entertainment' },
  ];

  const transactionId = 'txn-123';

  beforeEach(() => {
    jest.resetAllMocks();
  });

  // Mock global fetch
  const mockFetch = (url: string) => {
    if (url === '/api/categories/get') {
      return Promise.resolve({
        ok: true,
        json: () => Promise.resolve({ categories: categoriesMock }),
      } as Response);
    }
    if (url === '/api/transactions/categorize') {
      return Promise.resolve({
        ok: true,
        json: () => Promise.resolve({ success: true }),
      } as Response);
    }
    return Promise.reject(new Error('Unknown API endpoint'));
  };

  test('Initial Load and Display: renders dropdown with pre-selected category', async () => {
    global.fetch = jest.fn().mockImplementation(mockFetch);

    render(
      <CategorySelector
        transaction_id={transactionId}
        user_category_id='2' // pre-selected category "Transport"
      />
    );

    // Wait for categories to be loaded by waiting for a category option to appear
    await screen.findByRole('option', { name: 'Food' });

    // Now get the select element
    const select = screen.getByRole('combobox');
    expect(select).toBeInTheDocument();

    // The selected value should be '2'
    expect((select as HTMLSelectElement).value).toBe('2');

    // The selected option text should be "Transport"
    expect(screen.getByRole('option', { selected: true }).textContent).toBe('Transport');

    // The dropdown should contain all categories plus an "Uncategorized" option with empty value
    expect(screen.getByRole('option', { name: 'Uncategorized' })).toBeInTheDocument();
    categoriesMock.forEach((category) => {
      expect(screen.getByRole('option', { name: category.name })).toBeInTheDocument();
    });
  });

  test('User selects a new category: calls categorize API with correct data', async () => {
    const fetchMock = jest.fn().mockImplementation((url: string) => {
      if (url === '/api/categories/get') {
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve({ categories: categoriesMock }),
        } as Response);
      }
      if (url === '/api/transactions/categorize') {
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve({ success: true }),
        } as Response);
      }
      return Promise.reject(new Error('Unknown API endpoint'));
    });
    global.fetch = fetchMock;

    render(
      <CategorySelector
        transaction_id={transactionId}
        user_category_id='1' // initially "Food"
      />
    );

    // Wait for categories to be loaded
    await screen.findByRole('option', { name: 'Food' });

    // Get the select element
    const select = screen.getByRole('combobox');
    expect(select).toBeInTheDocument();

    // The selected value should be '1'
    expect((select as HTMLSelectElement).value).toBe('1');

    // Simulate user selecting a different category "Entertainment" (id: 3)
    fireEvent.change(select, { target: { value: '3' } });

    // Wait for the categorize API call to be made with correct body
    await waitFor(() => {
      expect(fetchMock).toHaveBeenCalledWith(
        '/api/transactions/categorize',
        expect.objectContaining({
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            transaction_id: transactionId,
            user_category_id: '3',
          }),
        })
      );
    });
  });

  test('Handle API error on selection: error is handled gracefully', async () => {
    // Mock fetch for categories get and categorize with error
    global.fetch = jest.fn().mockImplementation((url: string) => {
      if (url === '/api/categories/get') {
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve({ categories: categoriesMock }),
        } as Response);
      }
      if (url === '/api/transactions/categorize') {
        return Promise.reject(new Error('API error'));
      }
      return Promise.reject(new Error('Unknown API endpoint'));
    });

    render(<CategorySelector transaction_id={transactionId} user_category_id='1' />);

    // Wait for categories to be loaded
    await screen.findByRole('option', { name: 'Food' });

    const select = screen.getByRole('combobox');
    expect(select).toBeInTheDocument();

    // Simulate user selecting a different category "Entertainment" (id: 3)
    fireEvent.change(select, { target: { value: '3' } });

    // Wait for error message to appear in the DOM
    const errorMessage = await screen.findByText(/API error/i);
    expect(errorMessage).toBeInTheDocument();
  });
});
