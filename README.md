# NAVsync.io

## Project Description

NAVsync.io is a personal finance application designed to help users connect bank accounts, track transactions, and manage budgets effectively. Its goal is to provide a clear, intuitive, and secure way to manage personal finances.

## Key Features (MVP Focus)

- User account creation and secure login.
- Bank account connection via Plaid.
- Automatic transaction import and basic categorization.
- Budget category creation and management.
- Monthly budget setting and spending tracking.
- Dashboard for budget status and recent transactions.

## Technology Stack

- Next.js (App Router)
- TypeScript
- Supabase (PostgreSQL, Auth)
- Plaid (Financial Data Aggregation)
- shadcn/ui (UI Components)
- Tailwind CSS (Styling)
- Zod (Environment Variable Validation)
- Husky & lint-staged (Pre-commit Hooks)
- ESLint & Prettier (Linting & Formatting)
- GitHub Actions (CI/CD)

## Local Development Setup Instructions

### Prerequisites

- Node.js version 22.x (see `.nvmrc`)
- npm, yarn, or pnpm package manager

### Setup Steps

1. Clone the repository:

   ```bash
   git clone <repository-url>
   ```

2. Install dependencies:

   ```bash
   npm install
   ```

3. Set up environment variables (see next section).

4. Run the development server:

   ```bash
   npm run dev
   ```

5. Access the application at [http://localhost:3000](http://localhost:3000).

## Environment Variables Setup

- The `.env.local.example` file contains example environment variables needed to run the application.
- Copy `.env.local.example` to `.env.local` and fill in the required values.
- **Do not commit** `.env.local` to Git; it is included in `.gitignore`.
- Key services requiring environment variables include:
  - Supabase
  - Plaid
  - Email Service (optional for now)
  - `NEXTAUTH_SECRET` or `APP_SIGNING_SECRET`
- The application uses Zod for environment variable validation in [`src/env.mjs`](src/env.mjs). The app will fail to start if required variables are missing or invalid.

## Available npm Scripts

- `dev`: Starts the development server.
- `build`: Creates a production build.
- `start`: Starts a production server (after build).
- `lint`: Runs ESLint.
- `format`: Runs Prettier to format code.
- `type-check`: Runs TypeScript compiler to check types.
- `audit`: Runs `npm audit` for security vulnerabilities.

(Additional scripts may be available in `package.json`.)

## Contribution Guidelines

Contributions are welcome! Please follow the existing code style and ensure all checks pass before submitting a pull request.

## License Information

This project is currently proprietary. All rights reserved.
