'use client';

import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import CategorySelector from '@/components/categories/CategorySelector';

interface Transaction {
  id: string;
  amount: number;
  currency_code: string;
  transaction_date: string;
  authorized_date: string | null;
  posted_date: string | null;
  merchant_name: string | null;
  description: string;
  category_id: string | null;
  user_category_id: string | null;
  plaid_category: string[] | null;
  plaid_category_detailed: string[] | null;
  transaction_type: string;
  location: Record<string, unknown> | null;
  is_pending: boolean;
  is_recurring: boolean;
  status: string;
  tags: string[] | null;
  notes: string | null;
  created_at: string;
  updated_at: string;
  financial_accounts: {
    id: string;
    account_name: string;
    institution_name: string;
    account_type: string;
    account_subtype: string;
    mask: string | null;
  };
}

interface TransactionCardProps {
  transaction: Transaction;
}

/**
 * TransactionCard component displays detailed information about a single transaction.
 * It includes transaction amount, date, merchant, and a CategorySelector dropdown
 * to assign or change the user category for the transaction.
 *
 * @component
 * @param {Object} props - Component props
 * @param {Transaction} props.transaction - The transaction object to display
 * @returns {JSX.Element} The rendered transaction card
 */
export default function TransactionCard({ transaction }: TransactionCardProps) {
  const formatAmount = (amount: number, currencyCode: string) => {
    const isNegative = amount < 0;
    const absoluteAmount = Math.abs(amount);
    const formatted = new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currencyCode || 'USD',
    }).format(absoluteAmount);
    
    return { formatted, isNegative };
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const { formatted: amountFormatted, isNegative } = formatAmount(
    transaction.amount,
    transaction.currency_code
  );

  const displayName = transaction.merchant_name || transaction.description;
  const accountInfo = transaction.financial_accounts;

  return (
    <Card className='hover:shadow-md transition-shadow'>
      <CardContent className='p-4'>
        <div className='flex items-center justify-between'>
          {/* Left side - Transaction details */}
          <div className='flex-1 min-w-0'>
            <div className='flex items-center space-x-3'>
              {/* Transaction icon/indicator */}
              <div className={`w-3 h-3 rounded-full ${
                isNegative ? 'bg-red-500' : 'bg-green-500'
              }`} />
              
              {/* Main transaction info */}
              <div className='flex-1 min-w-0'>
                <h3 className='text-sm font-medium text-gray-900 truncate'>
                  {displayName}
                </h3>
                <div className='flex items-center space-x-2 text-xs text-gray-500'>
                  <span>{accountInfo.account_name}</span>
                  <span>•</span>
                  <span>{accountInfo.institution_name}</span>
                  {accountInfo.mask && (
                    <>
                      <span>•</span>
                      <span>****{accountInfo.mask}</span>
                    </>
                  )}
                </div>
              </div>
            </div>
            
            {/* Additional details */}
            <div className='mt-2 flex items-center space-x-4 text-xs text-gray-500'>
              <span>{formatDate(transaction.transaction_date)}</span>
              {transaction.is_pending && (
                <span className='px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full'>
                  Pending
                </span>
              )}
              {transaction.is_recurring && (
                <span className='px-2 py-1 bg-blue-100 text-blue-800 rounded-full'>
                  Recurring
                </span>
              )}
              {transaction.plaid_category && transaction.plaid_category.length > 0 && (
                <span className='px-2 py-1 bg-gray-100 text-gray-700 rounded-full'>
                  {transaction.plaid_category[0]}
                </span>
              )}
            </div>
          </div>
          
          {/* Right side - Amount */}
          <div className='ml-4 flex-shrink-0'>
            <div className={`text-lg font-semibold ${
              isNegative ? 'text-red-600' : 'text-green-600'
            }`}>
              {isNegative ? '-' : '+'}{amountFormatted}
            </div>
            <div className='text-xs text-gray-500 text-right'>
              {transaction.transaction_type}
            </div>
          </div>
        </div>
        
        <CategorySelector
          transaction_id={transaction.id}
          user_category_id={transaction.user_category_id}
        />

        {/* Notes section if present */}
        {transaction.notes && (
          <div className='mt-3 pt-3 border-t border-gray-100'>
            <p className='text-sm text-gray-600'>{transaction.notes}</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}