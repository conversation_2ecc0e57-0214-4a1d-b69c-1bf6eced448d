-- Financial accounts table (Plaid-linked accounts)
CREATE TABLE financial_accounts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    plaid_account_id TEXT NOT NULL,
    plaid_item_id TEXT NOT NULL,
    account_name TEXT NOT NULL,
    account_type TEXT NOT NULL CHECK (account_type IN ('checking', 'savings', 'credit', 'investment', 'loan', 'mortgage', 'other')),
    account_subtype TEXT,
    institution_name TEXT NOT NULL,
    institution_id TEXT,
    mask TEXT, -- Last 4 digits of account number
    current_balance DECIMAL(15,2),
    available_balance DECIMAL(15,2),
    currency_code TEXT DEFAULT 'USD',
    is_active BOOLEAN DEFAULT TRUE,
    last_synced_at TIMESTAMPTZ,
    plaid_metadata JSONB, -- Store additional Plaid data
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(user_id, plaid_account_id)
);

-- Indexes
CREATE INDEX idx_financial_accounts_user_id ON financial_accounts(user_id);
CREATE INDEX idx_financial_accounts_plaid_account_id ON financial_accounts(plaid_account_id);
CREATE INDEX idx_financial_accounts_institution_id ON financial_accounts(institution_id);
CREATE INDEX idx_financial_accounts_is_active ON financial_accounts(is_active);

-- Enable RLS
ALTER TABLE financial_accounts ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Users can view own accounts" ON financial_accounts
    FOR SELECT
    TO authenticated
    USING ((SELECT auth.uid()) = user_id);

CREATE POLICY "Users can insert own accounts" ON financial_accounts
    FOR INSERT
    TO authenticated
    WITH CHECK ((SELECT auth.uid()) = user_id);

CREATE POLICY "Users can update own accounts" ON financial_accounts
    FOR UPDATE
    TO authenticated
    USING ((SELECT auth.uid()) = user_id)
    WITH CHECK ((SELECT auth.uid()) = user_id);

CREATE POLICY "Users can delete own accounts" ON financial_accounts
    FOR DELETE
    TO authenticated
    USING ((SELECT auth.uid()) = user_id);

-- Trigger for updated_at
CREATE TRIGGER update_financial_accounts_updated_at BEFORE UPDATE ON financial_accounts
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Comments
COMMENT ON TABLE financial_accounts IS 'Bank accounts linked via Plaid integration';
COMMENT ON COLUMN financial_accounts.plaid_metadata IS 'Additional Plaid account metadata and sync information';
 